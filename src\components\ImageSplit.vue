<template>
  <div class="image-split-container">
    <div class="header">
      <h2>图片分割</h2>
      <div class="status-indicator" :class="{ 'has-image': hasSelectedImage }">
        <span v-if="hasSelectedImage" class="status-text success">✓ 已选中图片</span>
        <span v-else class="status-text warning">⚠ 未选中图片</span>
      </div>
    </div>

    <!-- 开始分割按钮放在顶部 -->
    <div class="top-action-section">
      <button class="start-split-btn" :class="{ 'disabled': !hasSelectedImage || isProcessing }"
        :disabled="!hasSelectedImage || isProcessing" @click="openSplitModal">
        <span v-if="isProcessing" class="loading-spinner-small"></span>
        <span>{{ isProcessing ? '处理中...' : '开始分割' }}</span>
      </button>
    </div>

    <div class="content">
      <div class="instruction-section">
        <h3>使用说明</h3>
        <ol>
          <li>在文档中选择要分割的图片</li>
          <li>点击上方的"开始分割"按钮</li>
          <li>使用工具栏中的工具：
            <ul>
              <li><strong>选择工具</strong>：点击已有的分割线或矩形进行调整</li>
              <li><strong>横线工具</strong>：点击添加横向分割线</li>
              <li><strong>竖线工具</strong>：点击添加竖向分割线</li>
              <li><strong>矩形工具</strong>：拖拽创建矩形选择区域</li>
            </ul>
          </li>
          <li>图片缩放查看：
            <ul>
              <li><strong>滚轮缩放</strong>：鼠标在图片上滚动滚轮进行放大缩小</li>
              <li><strong>重置缩放</strong>：缩放后工具栏会显示重置按钮，点击恢复100%</li>
              <li><strong>快捷键</strong>：Ctrl+/- 调整缩放，Ctrl+0 重置，双击重置</li>
              <li><strong>滚动查看</strong>：图片放大超出时可用滚动条查看</li>
            </ul>
          </li>
          <li>调整分割线位置：
            <ul>
              <li>点击分割线可以拖拽移动位置</li>
              <li>点击矩形可以拖拽移动，拖拽边角可以调整大小</li>
              <li>选中的分割线会高亮显示，并显示调整手柄</li>
            </ul>
          </li>
          <li>确认分割并插入到文档</li>
        </ol>
      </div>

      <!-- <div class="selection-info">
        <h3>当前选择</h3>
        <div class="selection-details">
          <p><strong>选择类型:</strong> {{ selectionType }}</p>
          <p><strong>图片数量:</strong> {{ imageCount }}</p>
          <p v-if="selectionText"><strong>文本内容:</strong> {{ selectionText.substring(0, 50) }}{{ selectionText.length > 50 ? '...' : '' }}</p>
        </div>
      </div> -->



      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
    </div>

    <!-- 分割弹窗 -->
    <div v-if="showSplitModal" class="modal-overlay" @click="closeSplitModal">
      <div class="split-modal" @click.stop>
        <div class="modal-header">
          <h3>图片分割工具</h3>
          <button class="close-btn" @click="closeSplitModal">×</button>
        </div>

                <div class="modal-content">
          <!-- 分割工具界面 -->
          <div>
            <!-- 工具栏 -->
            <div class="toolbar">
              <div class="tool-group">
                <button class="tool-btn" :class="{
                  active: currentTool === 'select'
                }" @click="setTool('select')" title="选择和调整工具">
                  <span class="tool-icon">🖱️</span>
                  <span class="tool-text">选择</span>
                </button>
                <button class="tool-btn" :class="{
                  active: currentTool === 'horizontal',
                  disabled: isToolDisabled('horizontal')
                }" :disabled="isToolDisabled('horizontal')" @click="setTool('horizontal')"
                  :title="getToolTooltip('horizontal')">
                  <span class="tool-icon">➖</span>
                  <span class="tool-text">横线</span>
                </button>
                <button class="tool-btn" :class="{
                  active: currentTool === 'vertical',
                  disabled: isToolDisabled('vertical')
                }" :disabled="isToolDisabled('vertical')" @click="setTool('vertical')"
                  :title="getToolTooltip('vertical')">
                  <span class="tool-icon">|</span>
                  <span class="tool-text">竖线</span>
                </button>
                <button class="tool-btn" :class="{
                  active: currentTool === 'rectangle',
                  disabled: isToolDisabled('rectangle')
                }" :disabled="isToolDisabled('rectangle')" @click="setTool('rectangle')"
                  :title="getToolTooltip('rectangle')">
                  <span class="tool-icon">⬜</span>
                  <span class="tool-text">矩形</span>
                </button>
              </div>
              
              <!-- 缩放控制组 -->
              <div class="zoom-controls">
                <button
                  v-if="zoomLevel !== 1.0"
                  class="zoom-btn reset-zoom"
                  @click="resetZoom"
                  title="重置缩放到100%"
                >
                  <span class="zoom-icon">🔄</span>
                  <span class="zoom-text">重置缩放</span>
                </button>
              </div>
              
              <div class="tool-actions">
                <button class="clear-btn" @click="clearSplits">清除所有</button>
                <button class="undo-btn" @click="undoLastSplit">撤销</button>
              </div>
            </div>

            <!-- 图片编辑区域 -->
            <div class="image-editor" ref="imageEditorContainer">
              <div class="image-container" ref="imageContainer">
                <!-- 图片显示 -->
                <div class="image-wrapper"
                     :class="{
                       'select-tool': currentTool === 'select',
                       'panning': isPanningImage
                     }"
                     :style="{ transform: `scale(${zoomLevel})`, transformOrigin: transformOrigin }"
                     @wheel="onImageWheel"
                     @dblclick="onImageDoubleClick">
                  <img v-if="currentImageUrl"
                       ref="imageElement"
                       :src="currentImageUrl"
                       class="split-image"
                       @load="onImageLoad"
                       @error="onImageError"
                       @mousedown="onImageMouseDown"
                       @mousemove="onImageMouseMove"
                       @mouseup="onImageMouseUp"
                       @mouseleave="onImageMouseLeave">

                  <!-- 分割线蒙层 -->
                  <div class="split-overlay" v-if="currentImageUrl">
                    <!-- 横向分割线 -->
                    <div v-for="(region, index) in horizontalLines"
                         :key="`h-${region.order}`"
                         class="split-line horizontal-line"
                         :class="{
                           'selected': selectedRegion === splitRegions.findIndex(r => r === region),
                           'hovered': hoverRegion === splitRegions.findIndex(r => r === region)
                         }"
                         :style="{ top: `${region.relativeY * 100}%` }"
                         @mousedown="onSplitLineMouseDown($event, splitRegions.findIndex(r => r === region))">
                      <div class="split-line-label">{{ region.order }}</div>
                    </div>

                    <!-- 竖向分割线 -->
                    <div v-for="(region, index) in verticalLines"
                         :key="`v-${region.order}`"
                         class="split-line vertical-line"
                         :class="{
                           'selected': selectedRegion === splitRegions.findIndex(r => r === region),
                           'hovered': hoverRegion === splitRegions.findIndex(r => r === region)
                         }"
                         :style="{ left: `${region.relativeX * 100}%` }"
                         @mousedown="onSplitLineMouseDown($event, splitRegions.findIndex(r => r === region))">
                      <div class="split-line-label">{{ region.order }}</div>
                    </div>

                    <!-- 矩形分割区域 -->
                    <div v-for="(region, index) in rectangles"
                         :key="`r-${region.order}`"
                         class="split-rectangle"
                         :class="{
                           'selected': selectedRegion === splitRegions.findIndex(r => r === region),
                           'hovered': hoverRegion === splitRegions.findIndex(r => r === region)
                         }"
                         :style="{
                           left: `${region.relativeX * 100}%`,
                           top: `${region.relativeY * 100}%`,
                           width: `${region.relativeWidth * 100}%`,
                           height: `${region.relativeHeight * 100}%`
                         }"
                         @mousedown="onSplitRectMouseDown($event, splitRegions.findIndex(r => r === region))">
                      <div class="split-rect-label">{{ region.order }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Loading 覆盖层 -->
              <div v-if="isLoadingImage" class="loading-overlay">
                <div class="loading-spinner-large"></div>
                <div class="loading-text">正在加载...</div>
              </div>

              <!-- 缩放提示 -->
              <div v-if="currentImageUrl && !isLoadingImage" class="zoom-tip">
                滚轮缩放 | 双击重置 | Ctrl+/-调整 | Ctrl+0重置 | 当前缩放: {{ Math.round(zoomLevel * 100) }}%
              </div>
            </div>
          </div>

 
        </div>

        <div class="modal-footer">
          <!-- 分割工具按钮 -->
          <div class="split-tool-buttons">
            <button class="cancel-btn" @click="closeSplitModal">取消</button>
            <button class="confirm-btn" :disabled="splitRegions.length === 0 || isProcessing" @click="confirmSplit">
              <span v-if="isProcessing" class="loading-spinner-small"></span>
              <span>{{ isProcessing ? '生成预览...' : `预览分割 (${getExpectedSplitCount()}个片段)` }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览弹窗 -->
    <div v-if="showPreview" class="preview-overlay" @click="closePreview">
      <div class="preview-modal" @click.stop>
        <div class="preview-header">
          <h3>分割预览</h3>
          <button class="close-btn" @click="closePreview">×</button>
        </div>

                 <div class="preview-content">
           <div class="preview-info-header">
             <h4>分割预览</h4>
             <!-- <p><strong>分割类型：</strong>{{ getSplitTypeName() }}</p> -->
             <!-- <p><strong>片段总数：</strong>{{ previewImages.length }}</p> -->
             <!-- <p><strong>插入位置：</strong>原图片下方</p> -->
           </div>
           
           <!-- 矩形分割：使用网格布局 -->
           <div v-if="getSplitType === 'rectangle'" class="preview-rectangle-grid" :style="previewGridStyle">
             <div v-for="image in previewImages" :key="image.order" class="preview-rectangle-item">
               <div class="preview-rectangle-number">{{ image.order }}</div>
               <img :src="image.ossUrl" :alt="`分割片段 ${image.order}`" class="preview-rectangle-image" @load="onPreviewImageLoad" @error="onPreviewImageError">
             </div>
           </div>

           <!-- 横线分割：垂直条带布局 -->
           <div v-else-if="getSplitType === 'horizontal_strip'" class="preview-strips preview-strips-vertical" :style="previewStripStyle">
             <div class="preview-strips-container">
               <div v-for="image in previewImages" :key="image.order" class="preview-strip-item">
                 <div class="preview-strip-number">{{ image.order }}</div>
                 <img :src="image.ossUrl" :alt="`分割片段 ${image.order}`" class="preview-strip-image" @load="onPreviewImageLoad" @error="onPreviewImageError">
               </div>
             </div>
           </div>

           <!-- 竖线分割：水平条带布局 -->
           <div v-else-if="getSplitType === 'vertical_strip'" class="preview-strips preview-strips-horizontal" :style="previewStripStyle">
             <div class="preview-strips-container">
               <div v-for="image in previewImages" :key="image.order" class="preview-strip-item">
                 <div class="preview-strip-number">{{ image.order }}</div>
                 <img :src="image.ossUrl" :alt="`分割片段 ${image.order}`" class="preview-strip-image" @load="onPreviewImageLoad" @error="onPreviewImageError">
               </div>
             </div>
           </div>

           <!-- 未知类型：回退到网格布局 -->
           <div v-else class="preview-grid" :style="previewGridStyle">
             <div v-for="image in previewImages" :key="image.order" class="preview-item">
               <div class="preview-image-wrapper">
                 <img :src="image.ossUrl" :alt="`分割片段 ${image.order}`" class="preview-image" @load="onPreviewImageLoad" @error="onPreviewImageError">
                 <div class="preview-image-overlay">
                   <span class="preview-image-number">{{ image.order }}</span>
                 </div>
               </div>
             </div>
           </div>
         </div>

        <div class="preview-footer">
          <button class="confirm-btn" @click="confirmInsertSplitImages">确认插入</button>
          <button class="cancel-btn" @click="closePreview">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import wsClient from './js/wsClient'

// 响应式数据
const hasSelectedImage = ref(false)
const selectionType = ref('无选择')
const imageCount = ref(0)
const selectionText = ref('')
const isProcessing = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const isLoadingImage = ref(false) // 图片加载状态
const currentImageInfo = ref(null) // 当前图片信息
const currentImageUrl = ref('') // 当前图片URL
const transformOrigin = ref('center center') // 缩放中心点

// 分割弹窗相关
const showSplitModal = ref(false)
const currentTool = ref('select') // 'select', 'horizontal', 'vertical', 'rectangle'
const splitRegions = ref([])
const selectedRegion = ref(-1)
const splitResults = ref([]) // 分割结果（保留用于兼容性）
const insertedImagePositions = ref([]) // 记录插入的图片位置信息（保留用于兼容性）
const showPreview = ref(false) // 显示预览界面
const previewImages = ref([]) // 预览图片列表

// 图片元素引用
const imageElement = ref(null)
const isDrawing = ref(false)
const startPoint = ref({ x: 0, y: 0 })
const currentImage = ref(null)
const watchedDir = ref('C:\\ww-wps-addon\\Temp') // 监控目录

// 添加缩放相关状态
const zoomLevel = ref(1.0) // 缩放比例，1.0为原始大小
const minZoom = ref(0.5) // 最小缩放比例
const maxZoom = ref(3.0) // 最大缩放比例
const zoomStep = ref(0.1) // 缩放步长
const imageEditorContainer = ref(null) // 图片编辑容器引用

// 添加拖拽调整相关状态
const isDragging = ref(false)
const dragMode = ref('') // 'move', 'resize-n', 'resize-s', 'resize-w', 'resize-e', 'resize-nw', 'resize-ne', 'resize-sw', 'resize-se', 'pan'
const dragStartPoint = ref({ x: 0, y: 0 })
const dragStartRegion = ref(null)
const hoverRegion = ref(-1) // 鼠标悬停的区域索引
const hoverHandle = ref('') // 鼠标悬停的调整手柄类型

// 添加图片拖拽相关状态
const isPanningImage = ref(false)
const panStartPoint = ref({ x: 0, y: 0 })
const panStartScroll = ref({ x: 0, y: 0 })
const imageContainer = ref(null)

// 检查选择状态的定时器
let selectionCheckTimer = null

// 新的图片和分割线事件处理方法
const onImageLoad = () => {
  isLoadingImage.value = false
  
  // 当图片加载完成后，获取图片的真实像素尺寸
  if (imageElement.value && currentImage.value) {
    const naturalWidth = imageElement.value.naturalWidth
    const naturalHeight = imageElement.value.naturalHeight
    
    if (naturalWidth && naturalHeight) {
      console.log('图片真实像素尺寸:', naturalWidth, 'x', naturalHeight)
      console.log('之前使用的WPS显示尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
      
      // 更新为真实的像素尺寸
      currentImage.value.originalWidth = naturalWidth
      currentImage.value.originalHeight = naturalHeight
      
      console.log('已更新为真实像素尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
    } else {
      console.warn('无法获取图片真实尺寸，继续使用WPS显示尺寸')
    }
  }
  
  console.log('图片加载完成')
}

const onImageError = () => {
  isLoadingImage.value = false
  errorMessage.value = '图片加载失败'
}

const onImageWheel = (event) => {
  event.preventDefault()

  const delta = event.deltaY > 0 ? -zoomStep.value : zoomStep.value
  const newZoom = Math.max(minZoom.value, Math.min(maxZoom.value, zoomLevel.value + delta))

  if (newZoom !== zoomLevel.value) {
    // 计算鼠标在图片中的相对位置作为缩放中心
    const rect = event.currentTarget.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 100
    const y = ((event.clientY - rect.top) / rect.height) * 100
    transformOrigin.value = `${x}% ${y}%`

    zoomLevel.value = newZoom
  }
}

const onImageDoubleClick = () => {
  if (zoomLevel.value !== 1.0) {
    resetZoom()
  }
}

const onImageMouseDown = (event) => {
  const rect = event.currentTarget.getBoundingClientRect()
  const x = (event.clientX - rect.left) / rect.width
  const y = (event.clientY - rect.top) / rect.height

  if (currentTool.value === 'select') {
    // 检查是否点击在分割线/矩形上
    const hitResult = checkHitRegionRelative(x, y)

    if (hitResult) {
      // 点击在分割区域上，开始拖拽调整
      selectedRegion.value = hitResult.index
      isDragging.value = true
      dragMode.value = hitResult.hitType === 'line' ? 'move' : hitResult.hitType
      dragStartPoint.value = { x, y }
      dragStartRegion.value = { ...hitResult.region }

      if (hitResult.hitType === 'resize') {
        dragMode.value = 'resize-' + hitResult.handleType
      }

      document.addEventListener('mousemove', onDocumentMouseMove)
      document.addEventListener('mouseup', onDocumentMouseUp)
    } else {
      // 点击在空白区域，开始图片拖拽
      isPanningImage.value = true
      panStartPoint.value = { x: event.clientX, y: event.clientY }
      if (imageContainer.value) {
        panStartScroll.value = {
          x: imageContainer.value.scrollLeft,
          y: imageContainer.value.scrollTop
        }
      }

      document.addEventListener('mousemove', onImagePanMove)
      document.addEventListener('mouseup', onImagePanEnd)
    }
    return
  }

  if (currentTool.value === 'horizontal') {
    addHorizontalSplit(y)
  } else if (currentTool.value === 'vertical') {
    addVerticalSplit(x)
  } else if (currentTool.value === 'rectangle') {
    startPoint.value = { x, y }
    isDrawing.value = true
  }
}

const onImageMouseMove = (event) => {
  // 处理矩形绘制
  if (isDrawing.value && currentTool.value === 'rectangle') {
    // 这里可以添加实时预览逻辑
  }
}

const onImageMouseUp = (event) => {
  if (isDrawing.value && currentTool.value === 'rectangle') {
    const rect = event.currentTarget.getBoundingClientRect()
    const x = (event.clientX - rect.left) / rect.width
    const y = (event.clientY - rect.top) / rect.height

    addRectangleSplit(startPoint.value.x, startPoint.value.y, x, y)
    isDrawing.value = false
  }
}

const onImageMouseLeave = () => {
  isDrawing.value = false
}

// 图片拖拽相关函数
const onImagePanMove = (event) => {
  if (!isPanningImage.value || !imageContainer.value) return

  const deltaX = event.clientX - panStartPoint.value.x
  const deltaY = event.clientY - panStartPoint.value.y

  imageContainer.value.scrollLeft = panStartScroll.value.x - deltaX
  imageContainer.value.scrollTop = panStartScroll.value.y - deltaY
}

const onImagePanEnd = () => {
  isPanningImage.value = false
  panStartPoint.value = { x: 0, y: 0 }
  panStartScroll.value = { x: 0, y: 0 }

  document.removeEventListener('mousemove', onImagePanMove)
  document.removeEventListener('mouseup', onImagePanEnd)
}

// 检查点击是否在分割线/矩形上（使用相对坐标）
const checkHitRegionRelative = (relativeX, relativeY) => {
  const tolerance = 0.02 // 相对坐标容差

  for (let i = splitRegions.value.length - 1; i >= 0; i--) {
    const region = splitRegions.value[i]

    if (region.type === 'horizontal') {
      // 检查是否点击在横线上
      if (Math.abs(relativeY - region.relativeY) <= tolerance) {
        return { index: i, region, hitType: 'line' }
      }
    } else if (region.type === 'vertical') {
      // 检查是否点击在竖线上
      if (Math.abs(relativeX - region.relativeX) <= tolerance) {
        return { index: i, region, hitType: 'line' }
      }
    } else if (region.type === 'rectangle') {
      // 检查是否点击在矩形内或边缘
      const left = region.relativeX
      const right = region.relativeX + region.relativeWidth
      const top = region.relativeY
      const bottom = region.relativeY + region.relativeHeight

      // 检查是否在矩形内
      if (relativeX >= left && relativeX <= right && relativeY >= top && relativeY <= bottom) {
        // 检查是否在边缘（用于调整大小）
        const handleType = getResizeHandleRelative(relativeX, relativeY, region)
        if (handleType) {
          return { index: i, region, hitType: 'resize', handleType }
        } else {
          return { index: i, region, hitType: 'move' }
        }
      }
    }
  }

  return null
}

// 获取矩形调整手柄类型（使用相对坐标）
const getResizeHandleRelative = (relativeX, relativeY, region) => {
  const handleSize = 0.02 // 相对坐标手柄大小
  const left = region.relativeX
  const right = region.relativeX + region.relativeWidth
  const top = region.relativeY
  const bottom = region.relativeY + region.relativeHeight

  // 检查四个角的调整手柄
  if (Math.abs(relativeX - left) <= handleSize && Math.abs(relativeY - top) <= handleSize) {
    return 'nw' // 左上角
  }
  if (Math.abs(relativeX - right) <= handleSize && Math.abs(relativeY - top) <= handleSize) {
    return 'ne' // 右上角
  }
  if (Math.abs(relativeX - left) <= handleSize && Math.abs(relativeY - bottom) <= handleSize) {
    return 'sw' // 左下角
  }
  if (Math.abs(relativeX - right) <= handleSize && Math.abs(relativeY - bottom) <= handleSize) {
    return 'se' // 右下角
  }

  // 检查四边的调整手柄
  if (Math.abs(relativeY - top) <= handleSize && relativeX >= left + handleSize && relativeX <= right - handleSize) {
    return 'n' // 上边
  }
  if (Math.abs(relativeY - bottom) <= handleSize && relativeX >= left + handleSize && relativeX <= right - handleSize) {
    return 's' // 下边
  }
  if (Math.abs(relativeX - left) <= handleSize && relativeY >= top + handleSize && relativeY <= bottom - handleSize) {
    return 'w' // 左边
  }
  if (Math.abs(relativeX - right) <= handleSize && relativeY >= top + handleSize && relativeY <= bottom - handleSize) {
    return 'e' // 右边
  }

  return null
}

// 分割线事件处理
const onSplitLineMouseDown = (event, index) => {
  event.preventDefault()
  event.stopPropagation()
  selectedRegion.value = index
  isDragging.value = true
  dragMode.value = 'move'

  const rect = event.currentTarget.closest('.image-wrapper').getBoundingClientRect()
  dragStartPoint.value = {
    x: (event.clientX - rect.left) / rect.width,
    y: (event.clientY - rect.top) / rect.height
  }
  dragStartRegion.value = { ...splitRegions.value[index] }

  document.addEventListener('mousemove', onDocumentMouseMove)
  document.addEventListener('mouseup', onDocumentMouseUp)
}

const onSplitRectMouseDown = (event, index) => {
  event.preventDefault()
  event.stopPropagation()
  selectedRegion.value = index
  isDragging.value = true
  dragMode.value = 'move'

  const rect = event.currentTarget.closest('.image-wrapper').getBoundingClientRect()
  dragStartPoint.value = {
    x: (event.clientX - rect.left) / rect.width,
    y: (event.clientY - rect.top) / rect.height
  }
  dragStartRegion.value = { ...splitRegions.value[index] }

  document.addEventListener('mousemove', onDocumentMouseMove)
  document.addEventListener('mouseup', onDocumentMouseUp)
}

const onResizeHandleMouseDown = (event, index, handleType) => {
  event.preventDefault()
  event.stopPropagation()
  selectedRegion.value = index
  isDragging.value = true
  dragMode.value = `resize-${handleType}`

  const rect = event.currentTarget.closest('.image-wrapper').getBoundingClientRect()
  dragStartPoint.value = {
    x: (event.clientX - rect.left) / rect.width,
    y: (event.clientY - rect.top) / rect.height
  }
  dragStartRegion.value = { ...splitRegions.value[index] }

  document.addEventListener('mousemove', onDocumentMouseMove)
  document.addEventListener('mouseup', onDocumentMouseUp)
}

const onDocumentMouseMove = (event) => {
  if (!isDragging.value || !dragStartRegion.value || selectedRegion.value === -1) return

  const imageWrapper = document.querySelector('.image-wrapper')
  if (!imageWrapper) return

  const rect = imageWrapper.getBoundingClientRect()
  const currentX = (event.clientX - rect.left) / rect.width
  const currentY = (event.clientY - rect.top) / rect.height

  const deltaX = currentX - dragStartPoint.value.x
  const deltaY = currentY - dragStartPoint.value.y

  const region = splitRegions.value[selectedRegion.value]

  if (dragMode.value === 'move') {
    if (region.type === 'horizontal') {
      region.relativeY = Math.max(0, Math.min(1, dragStartRegion.value.relativeY + deltaY))
    } else if (region.type === 'vertical') {
      region.relativeX = Math.max(0, Math.min(1, dragStartRegion.value.relativeX + deltaX))
    } else if (region.type === 'rectangle') {
      region.relativeX = Math.max(0, Math.min(1 - region.relativeWidth, dragStartRegion.value.relativeX + deltaX))
      region.relativeY = Math.max(0, Math.min(1 - region.relativeHeight, dragStartRegion.value.relativeY + deltaY))
    }
  } else if (dragMode.value.startsWith('resize-')) {
    // 处理矩形调整大小
    const handleType = dragMode.value.split('-')[1]
    resizeRectangleRelative(region, handleType, deltaX, deltaY)
  }
}

const onDocumentMouseUp = () => {
  isDragging.value = false
  dragMode.value = ''
  dragStartPoint.value = { x: 0, y: 0 }
  dragStartRegion.value = null

  document.removeEventListener('mousemove', onDocumentMouseMove)
  document.removeEventListener('mouseup', onDocumentMouseUp)
}

const resizeRectangleRelative = (region, handleType, deltaX, deltaY) => {
  const originalRegion = dragStartRegion.value
  const minSize = 0.02 // 最小尺寸（相对值）

  switch (handleType) {
    case 'n': // 上边
      region.relativeY = Math.max(0, Math.min(originalRegion.relativeY + originalRegion.relativeHeight - minSize, originalRegion.relativeY + deltaY))
      region.relativeHeight = originalRegion.relativeHeight - (region.relativeY - originalRegion.relativeY)
      break
    case 's': // 下边
      region.relativeHeight = Math.max(minSize, Math.min(1 - originalRegion.relativeY, originalRegion.relativeHeight + deltaY))
      break
    case 'w': // 左边
      region.relativeX = Math.max(0, Math.min(originalRegion.relativeX + originalRegion.relativeWidth - minSize, originalRegion.relativeX + deltaX))
      region.relativeWidth = originalRegion.relativeWidth - (region.relativeX - originalRegion.relativeX)
      break
    case 'e': // 右边
      region.relativeWidth = Math.max(minSize, Math.min(1 - originalRegion.relativeX, originalRegion.relativeWidth + deltaX))
      break
    case 'nw': // 左上角
      region.relativeX = Math.max(0, Math.min(originalRegion.relativeX + originalRegion.relativeWidth - minSize, originalRegion.relativeX + deltaX))
      region.relativeY = Math.max(0, Math.min(originalRegion.relativeY + originalRegion.relativeHeight - minSize, originalRegion.relativeY + deltaY))
      region.relativeWidth = originalRegion.relativeWidth - (region.relativeX - originalRegion.relativeX)
      region.relativeHeight = originalRegion.relativeHeight - (region.relativeY - originalRegion.relativeY)
      break
    case 'ne': // 右上角
      region.relativeY = Math.max(0, Math.min(originalRegion.relativeY + originalRegion.relativeHeight - minSize, originalRegion.relativeY + deltaY))
      region.relativeWidth = Math.max(minSize, Math.min(1 - originalRegion.relativeX, originalRegion.relativeWidth + deltaX))
      region.relativeHeight = originalRegion.relativeHeight - (region.relativeY - originalRegion.relativeY)
      break
    case 'sw': // 左下角
      region.relativeX = Math.max(0, Math.min(originalRegion.relativeX + originalRegion.relativeWidth - minSize, originalRegion.relativeX + deltaX))
      region.relativeWidth = originalRegion.relativeWidth - (region.relativeX - originalRegion.relativeX)
      region.relativeHeight = Math.max(minSize, Math.min(1 - originalRegion.relativeY, originalRegion.relativeHeight + deltaY))
      break
    case 'se': // 右下角
      region.relativeWidth = Math.max(minSize, Math.min(1 - originalRegion.relativeX, originalRegion.relativeWidth + deltaX))
      region.relativeHeight = Math.max(minSize, Math.min(1 - originalRegion.relativeY, originalRegion.relativeHeight + deltaY))
      break
  }
}

// 计算当前已使用的工具类型
const usedToolType = computed(() => {
  if (splitRegions.value.length === 0) {
    return null
  }
  return splitRegions.value[0].type
})

// 计算属性：分别获取不同类型的分割区域
const horizontalLines = computed(() =>
  splitRegions.value.filter(region => region.type === 'horizontal')
)

const verticalLines = computed(() =>
  splitRegions.value.filter(region => region.type === 'vertical')
)

const rectangles = computed(() =>
  splitRegions.value.filter(region => region.type === 'rectangle')
)

// 检查工具是否应该被禁用
const isToolDisabled = (toolType) => {
  // 选择工具始终可用
  if (toolType === 'select') {
    return false
  }
  
  const used = usedToolType.value
  if (!used) {
    return false // 没有使用任何工具时，所有工具都可用
  }
  return used !== toolType // 如果已使用的工具类型与当前工具不同，则禁用
}

// 计算预期的分割片段数量
const getExpectedSplitCount = () => {
  // 统计线条数量（横线和竖线）
  const lineCount = splitRegions.value.filter(region =>
    region.type === 'horizontal' || region.type === 'vertical'
  ).length

  // 统计矩形数量
  const rectangleCount = splitRegions.value.filter(region =>
    region.type === 'rectangle'
  ).length

  // n条线分割产生n+1个区域，矩形分割产生独立的区域
  return (lineCount > 0 ? lineCount + 1 : 0) + rectangleCount
}

// 获取工具提示文本
const getToolTooltip = (toolType) => {
  if (toolType === 'select') {
    return '选择和调整已有的分割线或矩形'
  }
  
  const used = usedToolType.value
  if (!used) {
    // 没有使用任何工具时的默认提示
    switch (toolType) {
      case 'horizontal': return '横向分割线'
      case 'vertical': return '竖向分割线'
      case 'rectangle': return '矩形选择'
      default: return ''
    }
  }

  if (used === toolType) {
    // 当前工具可用
    switch (toolType) {
      case 'horizontal': return '横向分割线 (当前工具)'
      case 'vertical': return '竖向分割线 (当前工具)'
      case 'rectangle': return '矩形选择 (当前工具)'
      default: return ''
    }
  } else {
    // 其他工具被禁用
    const usedToolName = used === 'horizontal' ? '横线' : used === 'vertical' ? '竖线' : '矩形'
    return `已使用${usedToolName}工具，请先清除所有分割线`
  }
}

// 检测点击是否在分割线/矩形上
const checkHitRegion = (x, y) => {
  if (!currentImage.value) return null

  const tolerance = 8 // 点击容差

  for (let i = splitRegions.value.length - 1; i >= 0; i--) {
    const region = splitRegions.value[i]
    
    if (region.type === 'horizontal') {
      // 检查是否点击在横线上
      if (Math.abs(y - region.y) <= tolerance) {
        return { index: i, region, hitType: 'line' }
      }
    } else if (region.type === 'vertical') {
      // 检查是否点击在竖线上
      if (Math.abs(x - region.x) <= tolerance) {
        return { index: i, region, hitType: 'line' }
      }
    } else if (region.type === 'rectangle') {
      // 检查是否点击在矩形内或边缘
      const left = region.x
      const right = region.x + region.width
      const top = region.y
      const bottom = region.y + region.height

      // 检查是否在矩形内
      if (x >= left && x <= right && y >= top && y <= bottom) {
        // 检查是否在边缘（用于调整大小）
        const handleType = getResizeHandle(x, y, region)
        if (handleType) {
          return { index: i, region, hitType: 'resize', handleType }
        } else {
          return { index: i, region, hitType: 'move' }
        }
      }
    }
  }

  return null
}

// 获取矩形调整手柄类型
const getResizeHandle = (x, y, region) => {
  const handleSize = 8
  const left = region.x
  const right = region.x + region.width
  const top = region.y
  const bottom = region.y + region.height

  // 检查四个角的调整手柄
  if (Math.abs(x - left) <= handleSize && Math.abs(y - top) <= handleSize) {
    return 'nw' // 左上角
  }
  if (Math.abs(x - right) <= handleSize && Math.abs(y - top) <= handleSize) {
    return 'ne' // 右上角
  }
  if (Math.abs(x - left) <= handleSize && Math.abs(y - bottom) <= handleSize) {
    return 'sw' // 左下角
  }
  if (Math.abs(x - right) <= handleSize && Math.abs(y - bottom) <= handleSize) {
    return 'se' // 右下角
  }

  // 检查四边的调整手柄
  if (Math.abs(y - top) <= handleSize && x >= left + handleSize && x <= right - handleSize) {
    return 'n' // 上边
  }
  if (Math.abs(y - bottom) <= handleSize && x >= left + handleSize && x <= right - handleSize) {
    return 's' // 下边
  }
  if (Math.abs(x - left) <= handleSize && y >= top + handleSize && y <= bottom - handleSize) {
    return 'w' // 左边
  }
  if (Math.abs(x - right) <= handleSize && y >= top + handleSize && y <= bottom - handleSize) {
    return 'e' // 右边
  }

  return null
}

// 获取鼠标光标样式
const getCursorStyle = (hitResult) => {
  if (!hitResult) return 'crosshair'

  if (hitResult.hitType === 'line') {
    return hitResult.region.type === 'horizontal' ? 'ns-resize' : 'ew-resize'
  } else if (hitResult.hitType === 'move') {
    return 'move'
  } else if (hitResult.hitType === 'resize') {
    const handleType = hitResult.handleType
    switch (handleType) {
      case 'n':
      case 's':
        return 'ns-resize'
      case 'w':
      case 'e':
        return 'ew-resize'
      case 'nw':
      case 'se':
        return 'nw-resize'
      case 'ne':
      case 'sw':
        return 'ne-resize'
      default:
        return 'move'
    }
  }

  return 'crosshair'
}

// 获取图片分割监控目录
const getImageSplitWatchDir = async () => {
  try {
    // 从WebSocket客户端获取图片分割监控目录
    if (wsClient) {
      const response = await wsClient.sendRequest('imageSplit', 'getWatchDir', {})
      if (response && response.watchDir) {
        return response.watchDir
      }
    }
    return 'C:\\ww-wps-addon\\Temp\\ImageSplit' // 默认图片分割监控目录
  } catch (error) {
    console.warn('获取图片分割监控目录失败，使用默认目录:', error)
    return 'C:\\ww-wps-addon\\Temp\\ImageSplit'
  }
}

// 打开分割弹窗
const openSplitModal = async () => {
  if (!hasSelectedImage.value || isProcessing.value) {
    return
  }

  try {
    isProcessing.value = true
    errorMessage.value = ''

    // 获取图片分割监控目录
    watchedDir.value = await getImageSplitWatchDir()

    // 提取并保存图片到监控目录
    const imageInfo = await saveSelectedImageToWatchDir()

    if (imageInfo) {
      // 保存图片信息并显示分割弹窗
      currentImageInfo.value = imageInfo
      showSplitModal.value = true

      // 重置分割状态
      splitRegions.value = []
      selectedRegion.value = -1
      currentTool.value = 'select'

      // 等待弹窗渲染完成后开始加载图片
      await nextTick() // 等待Vue更新DOM
      setTimeout(async () => {
        try {
          console.log('开始等待图片上传到OSS...')
          isLoadingImage.value = true

          // 清空当前图片URL，显示loading状态
          currentImageUrl.value = ''
          console.log('等待OSS上传完成...')

          // 等待WebSocket事件通知OSS上传成功
          // 实际的图片加载将在WebSocket事件处理器中完成

        } catch (loadError) {
          console.error('加载Loading图片失败:', loadError)
          errorMessage.value = '加载图片失败: ' + loadError.message
          isLoadingImage.value = false
        }
      }, 300) // 等待300ms确保弹窗完全渲染
    }

  } catch (error) {
    console.error('打开分割弹窗失败:', error)
    errorMessage.value = '打开分割工具失败: ' + error.message
  } finally {
    isProcessing.value = false
  }
}

// 保存选中图片到监控目录
const saveSelectedImageToWatchDir = async () => {
  try {
    const selection = window.Application.Selection
    const range = selection.Range

    if (!range || !range.InlineShapes || range.InlineShapes.Count === 0) {
      throw new Error('未找到选中的图片')
    }

    // 获取第一张图片
    const shape = range.InlineShapes.Item(1)

    if (!shape || (shape.Type !== 3 && shape.Type !== window.Application?.Enum?.wdInlineShapePicture)) {
      throw new Error('选中的不是图片')
    }

    // 生成唯一文件名
    const timestamp = Date.now()
    const fileName = `image_split_${timestamp}.png`
    const fullPath = `${watchedDir.value}\\${fileName}`

    // 确保监控目录存在
    const fileSystem = window.Application.FileSystem
    // 保存图片到监控目录
    shape.SaveAsPicture(fullPath)

    // 验证文件是否保存成功
    if (!fileSystem.Exists(fullPath)) {
      throw new Error('图片保存失败')
    }

    console.log('图片已保存到:', fullPath)
    
    // 获取WPS中的显示尺寸（注意：这不是图片的实际像素尺寸）
    const wpsDisplayWidth = shape.Width || 400
    const wpsDisplayHeight = shape.Height || 300
    
    console.log('WPS图片信息:')
    console.log('  - 显示尺寸（Points）:', wpsDisplayWidth, 'x', wpsDisplayHeight)
    console.log('  - 注意：这不是图片的实际像素尺寸')
    console.log('  - 实际像素尺寸将在OSS图片加载后获取')

    // 通知WebSocket服务器关联文件与客户端
    try {
      if (wsClient) {
        console.log('[DEBUG] 准备关联文件:', fileName, '客户端ID:', wsClient.getClientId())
        const result = await wsClient.sendRequest('imageSplit', 'associateFile', {
          fileName: fileName
        })
        console.log('[DEBUG] 关联文件响应:', result)
        console.log('已通知服务器关联图片文件:', fileName)
      }
    } catch (wsError) {
      console.warn('通知服务器关联文件失败:', wsError)
    }

    // 返回包含图片信息的对象，等待服务端上传通知
    return {
      path: fullPath,
      fileName: fileName,
      width: wpsDisplayWidth,
      height: wpsDisplayHeight,
      wpsDisplayWidth: wpsDisplayWidth,  // 保存WPS显示尺寸用于插入时计算
      wpsDisplayHeight: wpsDisplayHeight, // 保存WPS显示尺寸用于插入时计算
      needsDemo: true // 初始状态，等待服务端上传成功通知
    }

  } catch (error) {
    console.error('保存图片到监控目录失败:', error)
    throw error
  }
}

// 加载演示图片到Canvas (已废弃，使用img+蒙层方案)
const loadDemoImageToCanvas = async (width, height) => {
  // 不再使用Canvas，直接返回
  return Promise.resolve()
}

// 加载OSS图片到Canvas (已废弃，使用img+蒙层方案)
const loadImageFromOSS = async (ossUrl, originalWidth, originalHeight) => {
  // 不再使用Canvas，直接返回
  return Promise.resolve()
}

// 工具选择函数







// 关闭分割弹窗
const closeSplitModal = () => {
  showSplitModal.value = false
  splitRegions.value = []
  selectedRegion.value = -1
  hoverRegion.value = -1
  hoverHandle.value = ''
  isDragging.value = false
  dragMode.value = ''
  dragStartPoint.value = { x: 0, y: 0 }
  dragStartRegion.value = null
  currentImage.value = null
  splitResults.value = []
  insertedImagePositions.value = []
  showPreview.value = false
  previewImages.value = []
  // 重置缩放状态
  zoomLevel.value = 1.0
  // 重置图片拖拽状态
  isPanningImage.value = false
  panStartPoint.value = { x: 0, y: 0 }
  panStartScroll.value = { x: 0, y: 0 }
}

// 获取分割类型
const getSplitType = computed(() => {
  if (previewImages.value.length === 0) return 'unknown'
  return previewImages.value[0].type
})

// 计算预览图片的显示尺寸
const previewImageDimensions = computed(() => {
  if (!currentImageInfo.value) {
    return { width: 200, height: 200 }
  }

  const originalWidth = currentImageInfo.value.wpsDisplayWidth
  const originalHeight = currentImageInfo.value.wpsDisplayHeight

  // 根据分割类型调整最大尺寸
  const splitType = getSplitType.value
  let MAX_WIDTH, MAX_HEIGHT, MIN_WIDTH, MIN_HEIGHT

  if (splitType === 'rectangle') {
    // 矩形分割可能有各种尺寸，给更大的范围
    MAX_WIDTH = 350
    MAX_HEIGHT = 350
    MIN_WIDTH = 100
    MIN_HEIGHT = 100
  } else {
    // 条带分割通常是长条形
    MAX_WIDTH = 300
    MAX_HEIGHT = 300
    MIN_WIDTH = 120
    MIN_HEIGHT = 120
  }

  // 计算缩放比例
  const widthRatio = MAX_WIDTH / originalWidth
  const heightRatio = MAX_HEIGHT / originalHeight
  const scale = Math.min(widthRatio, heightRatio, 1) // 不放大，只缩小

  // 计算实际显示尺寸
  let displayWidth = originalWidth * scale
  let displayHeight = originalHeight * scale

  // 确保不小于最小尺寸
  if (displayWidth < MIN_WIDTH || displayHeight < MIN_HEIGHT) {
    const minScale = Math.max(MIN_WIDTH / originalWidth, MIN_HEIGHT / originalHeight)
    displayWidth = originalWidth * minScale
    displayHeight = originalHeight * minScale
  }

  return {
    width: Math.round(displayWidth),
    height: Math.round(displayHeight)
  }
})

// 计算预览网格的CSS样式
const previewGridStyle = computed(() => {
  const dimensions = previewImageDimensions.value
  return {
    '--preview-item-width': `${dimensions.width}px`,
    '--preview-item-height': `${dimensions.height}px`
  }
})

// 计算条带布局的CSS样式
const previewStripStyle = computed(() => {
  if (!currentImageInfo.value) {
    return {
      '--strip-container-width': '600px',
      '--strip-container-height': '400px'
    }
  }

  const originalWidth = currentImageInfo.value.wpsDisplayWidth
  const originalHeight = currentImageInfo.value.wpsDisplayHeight

  // 条带布局的最大尺寸
  const MAX_STRIP_WIDTH = 800
  const MAX_STRIP_HEIGHT = 500
  const MIN_STRIP_WIDTH = 300
  const MIN_STRIP_HEIGHT = 200

  // 计算条带容器的尺寸（保持原图比例）
  const widthRatio = MAX_STRIP_WIDTH / originalWidth
  const heightRatio = MAX_STRIP_HEIGHT / originalHeight
  const scale = Math.min(widthRatio, heightRatio, 1)

  let containerWidth = originalWidth * scale
  let containerHeight = originalHeight * scale

  // 确保不小于最小尺寸
  if (containerWidth < MIN_STRIP_WIDTH || containerHeight < MIN_STRIP_HEIGHT) {
    const minScale = Math.max(MIN_STRIP_WIDTH / originalWidth, MIN_STRIP_HEIGHT / originalHeight)
    containerWidth = originalWidth * minScale
    containerHeight = originalHeight * minScale
  }

  return {
    '--strip-container-width': `${Math.round(containerWidth)}px`,
    '--strip-container-height': `${Math.round(containerHeight)}px`
  }
})

// 设置当前工具
const setTool = (tool) => {
  // 检查工具是否被禁用
  if (isToolDisabled(tool)) {
    console.warn(`工具 ${tool} 被禁用，当前已使用工具: ${usedToolType.value}`)
    return
  }
  
  // 清除当前选择状态
  selectedRegion.value = -1
  hoverRegion.value = -1
  hoverHandle.value = ''
  isDragging.value = false
  dragMode.value = ''
  
  currentTool.value = tool
  
  // 重绘Canvas以更新显示
  redrawCanvas()
}

// Canvas鼠标事件处理
const onCanvasMouseDown = (event) => {
  if (!currentImage.value) return

  const canvas = imageCanvas.value
  const rect = canvas.getBoundingClientRect()
  const canvasX = event.clientX - rect.left
  const canvasY = event.clientY - rect.top

  // 检查点击是否在图片区域内
  const { offsetX, offsetY, displayWidth, displayHeight } = currentImage.value
  if (canvasX < offsetX || canvasX > offsetX + displayWidth || 
      canvasY < offsetY || canvasY > offsetY + displayHeight) {
    return // 点击在图片外，忽略
  }

  // 转换为相对于图片的坐标，并确保在有效范围内
  const x = Math.max(0, Math.min(displayWidth, canvasX - offsetX))
  const y = Math.max(0, Math.min(displayHeight, canvasY - offsetY))

  console.log(`用户点击: Canvas坐标(${canvasX}, ${canvasY}) -> 图片相对坐标(${x}, ${y})`)

  // 检查是否点击在现有的分割线/矩形上
  const hitResult = checkHitRegion(x, y)
  
  if (currentTool.value === 'select') {
    // 选择工具：只处理拖拽调整
    if (hitResult) {
      console.log('选择工具：开始拖拽调整:', hitResult)
      isDragging.value = true
      dragMode.value = hitResult.hitType === 'line' ? 'move' : hitResult.hitType
      dragStartPoint.value = { x, y }
      dragStartRegion.value = { ...hitResult.region }
      selectedRegion.value = hitResult.index
      
      if (hitResult.hitType === 'resize') {
        dragMode.value = 'resize-' + hitResult.handleType
      }
      
      // 更新光标样式
      canvas.style.cursor = getCursorStyle(hitResult)
    } else {
      // 点击空白区域，取消选择
      selectedRegion.value = -1
      redrawCanvas()
    }
  } else {
    // 其他工具：优先处理拖拽调整，否则添加新分割线
    if (hitResult) {
      // 点击在现有分割线/矩形上，开始拖拽调整
      console.log('点击在分割区域上，开始拖拽调整:', hitResult)
      isDragging.value = true
      dragMode.value = hitResult.hitType === 'line' ? 'move' : hitResult.hitType
      dragStartPoint.value = { x, y }
      dragStartRegion.value = { ...hitResult.region }
      selectedRegion.value = hitResult.index
      
      if (hitResult.hitType === 'resize') {
        dragMode.value = 'resize-' + hitResult.handleType
      }
      
      // 更新光标样式
      canvas.style.cursor = getCursorStyle(hitResult)
    } else {
      // 点击在空白区域，添加新的分割线/矩形
      selectedRegion.value = -1
      isDrawing.value = true
      startPoint.value = { x, y }

      if (currentTool.value === 'horizontal') {
        // 横向分割线：立即添加
        addHorizontalSplit(y)
        isDrawing.value = false
      } else if (currentTool.value === 'vertical') {
        // 竖向分割线：立即添加
        addVerticalSplit(x)
        isDrawing.value = false
      }
      // 矩形工具需要在mouseup时处理
    }
  }
}

const onCanvasMouseMove = (event) => {
  if (!currentImage.value) return

  const canvas = imageCanvas.value
  const rect = canvas.getBoundingClientRect()
  const canvasX = event.clientX - rect.left
  const canvasY = event.clientY - rect.top

  // 转换为相对于图片的坐标，并确保在有效范围内
  const { offsetX, offsetY, displayWidth, displayHeight } = currentImage.value
  const x = Math.max(0, Math.min(displayWidth, canvasX - offsetX))
  const y = Math.max(0, Math.min(displayHeight, canvasY - offsetY))

  if (isDragging.value) {
    // 拖拽调整模式
    handleDragging(x, y)
  } else if (isDrawing.value && currentTool.value === 'rectangle') {
    // 绘制矩形模式
    redrawCanvas()
    drawTemporaryRectangle(startPoint.value.x, startPoint.value.y, x, y)
  } else {
    // 悬停检测模式
    const hitResult = checkHitRegion(x, y)
    canvas.style.cursor = getCursorStyle(hitResult)
    
    // 更新悬停状态
    if (hitResult) {
      hoverRegion.value = hitResult.index
      hoverHandle.value = hitResult.handleType || ''
    } else {
      hoverRegion.value = -1
      hoverHandle.value = ''
    }
  }
}

const onCanvasMouseUp = (event) => {
  if (!currentImage.value) return

  if (isDragging.value) {
    // 结束拖拽调整
    isDragging.value = false
    dragMode.value = ''
    dragStartPoint.value = { x: 0, y: 0 }
    dragStartRegion.value = null
    console.log('拖拽调整完成')
  } else if (isDrawing.value) {
    // 结束绘制
    if (currentTool.value === 'rectangle') {
      const canvas = imageCanvas.value
      const rect = canvas.getBoundingClientRect()
      const canvasX = event.clientX - rect.left
      const canvasY = event.clientY - rect.top

      // 转换为相对于图片的坐标，并确保在有效范围内
      const { offsetX, offsetY, displayWidth, displayHeight } = currentImage.value
      const x = Math.max(0, Math.min(displayWidth, canvasX - offsetX))
      const y = Math.max(0, Math.min(displayHeight, canvasY - offsetY))

      // 添加矩形分割区域
      addRectangleSplit(startPoint.value.x, startPoint.value.y, x, y)
    }
    
    isDrawing.value = false
  }
}

// 处理拖拽调整
const handleDragging = (x, y) => {
  if (!dragStartRegion.value || selectedRegion.value === -1) return

  const region = splitRegions.value[selectedRegion.value]
  const deltaX = x - dragStartPoint.value.x
  const deltaY = y - dragStartPoint.value.y

  if (region.type === 'horizontal' && dragMode.value === 'move') {
    // 调整横线Y坐标
    region.y = Math.max(0, Math.min(currentImage.value.displayHeight, dragStartRegion.value.y + deltaY))
    // 更新相对坐标
    region.relativeY = region.y / currentImage.value.displayHeight
  } else if (region.type === 'vertical' && dragMode.value === 'move') {
    // 调整竖线X坐标
    region.x = Math.max(0, Math.min(currentImage.value.displayWidth, dragStartRegion.value.x + deltaX))
    // 更新相对坐标
    region.relativeX = region.x / currentImage.value.displayWidth
  } else if (region.type === 'rectangle') {
    if (dragMode.value === 'move') {
      // 移动矩形
      region.x = Math.max(0, Math.min(currentImage.value.displayWidth - region.width, dragStartRegion.value.x + deltaX))
      region.y = Math.max(0, Math.min(currentImage.value.displayHeight - region.height, dragStartRegion.value.y + deltaY))
      // 更新相对坐标
      region.relativeX = region.x / currentImage.value.displayWidth
      region.relativeY = region.y / currentImage.value.displayHeight
    } else if (dragMode.value.startsWith('resize-')) {
      // 调整矩形大小
      const handleType = dragMode.value.split('-')[1]
      resizeRectangle(region, handleType, deltaX, deltaY)
      // 更新相对坐标
      region.relativeX = region.x / currentImage.value.displayWidth
      region.relativeY = region.y / currentImage.value.displayHeight
      region.relativeWidth = region.width / currentImage.value.displayWidth
      region.relativeHeight = region.height / currentImage.value.displayHeight
    }
  }

  redrawCanvas()
}

// 调整矩形大小
const resizeRectangle = (region, handleType, deltaX, deltaY) => {
  const originalRegion = dragStartRegion.value
  const minSize = 20 // 最小尺寸
  const maxWidth = currentImage.value.displayWidth
  const maxHeight = currentImage.value.displayHeight

  switch (handleType) {
    case 'n': // 上边
      region.y = Math.max(0, Math.min(originalRegion.y + originalRegion.height - minSize, originalRegion.y + deltaY))
      region.height = originalRegion.height - (region.y - originalRegion.y)
      break
    case 's': // 下边
      region.height = Math.max(minSize, Math.min(maxHeight - originalRegion.y, originalRegion.height + deltaY))
      break
    case 'w': // 左边
      region.x = Math.max(0, Math.min(originalRegion.x + originalRegion.width - minSize, originalRegion.x + deltaX))
      region.width = originalRegion.width - (region.x - originalRegion.x)
      break
    case 'e': // 右边
      region.width = Math.max(minSize, Math.min(maxWidth - originalRegion.x, originalRegion.width + deltaX))
      break
    case 'nw': // 左上角
      region.x = Math.max(0, Math.min(originalRegion.x + originalRegion.width - minSize, originalRegion.x + deltaX))
      region.y = Math.max(0, Math.min(originalRegion.y + originalRegion.height - minSize, originalRegion.y + deltaY))
      region.width = originalRegion.width - (region.x - originalRegion.x)
      region.height = originalRegion.height - (region.y - originalRegion.y)
      break
    case 'ne': // 右上角
      region.y = Math.max(0, Math.min(originalRegion.y + originalRegion.height - minSize, originalRegion.y + deltaY))
      region.width = Math.max(minSize, Math.min(maxWidth - originalRegion.x, originalRegion.width + deltaX))
      region.height = originalRegion.height - (region.y - originalRegion.y)
      break
    case 'sw': // 左下角
      region.x = Math.max(0, Math.min(originalRegion.x + originalRegion.width - minSize, originalRegion.x + deltaX))
      region.width = originalRegion.width - (region.x - originalRegion.x)
      region.height = Math.max(minSize, Math.min(maxHeight - originalRegion.y, originalRegion.height + deltaY))
      break
    case 'se': // 右下角
      region.width = Math.max(minSize, Math.min(maxWidth - originalRegion.x, originalRegion.width + deltaX))
      region.height = Math.max(minSize, Math.min(maxHeight - originalRegion.y, originalRegion.height + deltaY))
      break
  }
}

// 坐标转换函数：像素坐标转相对坐标
const pixelToRelative = (pixelX, pixelY) => {
  if (!currentImage.value) return { x: 0, y: 0 }
  return {
    x: pixelX / currentImage.value.displayWidth,
    y: pixelY / currentImage.value.displayHeight
  }
}

// 坐标转换函数：相对坐标转像素坐标
const relativeToPixel = (relativeX, relativeY) => {
  if (!currentImage.value) return { x: 0, y: 0 }
  return {
    x: relativeX * currentImage.value.displayWidth,
    y: relativeY * currentImage.value.displayHeight
  }
}

// 更新所有分割线的显示坐标（缩放时调用）
const updateSplitRegionsDisplayCoords = () => {
  if (!currentImage.value) return

  splitRegions.value.forEach(region => {
    if (region.type === 'horizontal' && region.relativeY !== undefined) {
      region.y = region.relativeY * currentImage.value.displayHeight
    } else if (region.type === 'vertical' && region.relativeX !== undefined) {
      region.x = region.relativeX * currentImage.value.displayWidth
    } else if (region.type === 'rectangle' && region.relativeX !== undefined) {
      region.x = region.relativeX * currentImage.value.displayWidth
      region.y = region.relativeY * currentImage.value.displayHeight
      region.width = region.relativeWidth * currentImage.value.displayWidth
      region.height = region.relativeHeight * currentImage.value.displayHeight
    }
  })
}

// 添加横向分割线（参数为相对坐标0-1）
const addHorizontalSplit = (relativeY) => {
  const region = {
    type: 'horizontal',
    relativeY: relativeY, // 相对坐标（0-1）
    order: splitRegions.value.length + 1
  }
  splitRegions.value.push(region)
}

// 添加竖向分割线（参数为相对坐标0-1）
const addVerticalSplit = (relativeX) => {
  const region = {
    type: 'vertical',
    relativeX: relativeX, // 相对坐标（0-1）
    order: splitRegions.value.length + 1
  }
  splitRegions.value.push(region)
}

// 添加矩形分割区域（参数为相对坐标0-1）
const addRectangleSplit = (x1, y1, x2, y2) => {
  const left = Math.min(x1, x2)
  const top = Math.min(y1, y2)
  const width = Math.abs(x2 - x1)
  const height = Math.abs(y2 - y1)

  // 最小尺寸检查（相对坐标）
  if (width < 0.02 || height < 0.02) {
    return
  }

  const region = {
    type: 'rectangle',
    relativeX: left, // 相对坐标（0-1）
    relativeY: top,
    relativeWidth: width,
    relativeHeight: height,
    order: splitRegions.value.length + 1
  }
  splitRegions.value.push(region)
}

// 重绘Canvas
const redrawCanvas = () => {
  if (!currentImage.value || !imageCanvas.value) return

  const canvas = imageCanvas.value
  const ctx = canvas.getContext('2d')
  const { canvasWidth, canvasHeight, displayWidth, displayHeight, offsetX, offsetY } = currentImage.value

  // 设置Canvas尺寸（确保Canvas能容纳缩放后的图片）
  canvas.width = canvasWidth
  canvas.height = canvasHeight

  // 清除Canvas
  ctx.clearRect(0, 0, canvasWidth, canvasHeight)

  // 重绘背景图片
  if (currentImage.value.isDemo) {
    // 如果是演示图片，重新绘制演示背景
    redrawDemoBackground(ctx, canvasWidth, canvasHeight)
  } else if (currentImage.value.element) {
    // 清空Canvas背景
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)

    // 如果有真实图片元素，绘制图片（居中）
    ctx.drawImage(
      currentImage.value.element,
      offsetX, offsetY,
      displayWidth, displayHeight
    )
  }

  // 绘制所有分割线和矩形
  splitRegions.value.forEach((region, index) => {
    drawSplitRegion(ctx, region, index)
  })
}

// 重绘演示背景
const redrawDemoBackground = (ctx, canvasWidth, canvasHeight) => {
  if (!currentImage.value) return

  const { displayWidth, displayHeight, offsetX, offsetY, originalWidth, originalHeight } = currentImage.value

  // 清空Canvas背景
  ctx.fillStyle = '#f0f0f0'
  ctx.fillRect(0, 0, canvasWidth, canvasHeight)

  // 创建渐变背景（在图片显示区域）
  const gradient = ctx.createLinearGradient(offsetX, offsetY, offsetX + displayWidth, offsetY + displayHeight)
  gradient.addColorStop(0, '#4CAF50')
  gradient.addColorStop(0.5, '#2196F3')
  gradient.addColorStop(1, '#FF9800')

  ctx.fillStyle = gradient
  ctx.fillRect(offsetX, offsetY, displayWidth, displayHeight)

  // 添加网格线
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
  ctx.lineWidth = 1
  const gridSize = 50

  for (let x = 0; x <= displayWidth; x += gridSize) {
    ctx.beginPath()
    ctx.moveTo(offsetX + x, offsetY)
    ctx.lineTo(offsetX + x, offsetY + displayHeight)
    ctx.stroke()
  }

  for (let y = 0; y <= displayHeight; y += gridSize) {
    ctx.beginPath()
    ctx.moveTo(offsetX, offsetY + y)
    ctx.lineTo(offsetX + displayWidth, offsetY + y)
    ctx.stroke()
  }

  // 添加文字
  ctx.fillStyle = 'white'
  ctx.font = 'bold 24px Arial'
  ctx.textAlign = 'center'
  ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
  ctx.shadowBlur = 4
  ctx.fillText('演示图片', offsetX + displayWidth / 2, offsetY + displayHeight / 2 - 30)

  ctx.font = '16px Arial'
  ctx.fillText('(用于分割工具演示)', offsetX + displayWidth / 2, offsetY + displayHeight / 2)
  ctx.fillText(`原始尺寸: ${originalWidth} × ${originalHeight}`, offsetX + displayWidth / 2, offsetY + displayHeight / 2 + 25)
  ctx.fillText(`显示尺寸: ${Math.round(displayWidth)} × ${Math.round(displayHeight)}`, offsetX + displayWidth / 2, offsetY + displayHeight / 2 + 50)
}

// 绘制分割区域
const drawSplitRegion = (ctx, region, index) => {
  if (!currentImage.value) return

  const { offsetX, offsetY, displayWidth, displayHeight } = currentImage.value
  const isSelected = selectedRegion.value === index
  const isHovered = hoverRegion.value === index
  
  ctx.save()

  if (region.type === 'horizontal') {
    // 绘制横向分割线
    ctx.strokeStyle = isSelected ? '#ff0000' : isHovered ? '#ff6666' : '#ff4444'
    ctx.lineWidth = isSelected ? 3 : 2
    ctx.setLineDash(isSelected ? [8, 4] : [5, 5])
    ctx.beginPath()
    ctx.moveTo(offsetX, offsetY + region.y)
    ctx.lineTo(offsetX + displayWidth, offsetY + region.y)
    ctx.stroke()

    // 绘制序号标识
    ctx.fillStyle = isSelected ? '#ff0000' : isHovered ? '#ff6666' : '#ff4444'
    ctx.beginPath()
    ctx.arc(offsetX + 30, offsetY + region.y, isSelected ? 18 : 15, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = 'white'
    ctx.font = `bold ${isSelected ? '16px' : '14px'} Arial`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(region.order.toString(), offsetX + 30, offsetY + region.y)

    // 选中状态时绘制调整手柄
    if (isSelected) {
      drawLineHandles(ctx, offsetX, offsetY + region.y, offsetX + displayWidth, offsetY + region.y, 'horizontal')
    }

  } else if (region.type === 'vertical') {
    // 绘制竖向分割线
    ctx.strokeStyle = isSelected ? '#0000ff' : isHovered ? '#6666ff' : '#4444ff'
    ctx.lineWidth = isSelected ? 3 : 2
    ctx.setLineDash(isSelected ? [8, 4] : [5, 5])
    ctx.beginPath()
    ctx.moveTo(offsetX + region.x, offsetY)
    ctx.lineTo(offsetX + region.x, offsetY + displayHeight)
    ctx.stroke()

    // 绘制序号标识
    ctx.fillStyle = isSelected ? '#0000ff' : isHovered ? '#6666ff' : '#4444ff'
    ctx.beginPath()
    ctx.arc(offsetX + region.x, offsetY + 30, isSelected ? 18 : 15, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = 'white'
    ctx.font = `bold ${isSelected ? '16px' : '14px'} Arial`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(region.order.toString(), offsetX + region.x, offsetY + 30)

    // 选中状态时绘制调整手柄
    if (isSelected) {
      drawLineHandles(ctx, offsetX + region.x, offsetY, offsetX + region.x, offsetY + displayHeight, 'vertical')
    }

  } else if (region.type === 'rectangle') {
    // 绘制矩形选择区域
    ctx.strokeStyle = isSelected ? '#00aa00' : isHovered ? '#66dd66' : '#44ff44'
    ctx.lineWidth = isSelected ? 3 : 2
    ctx.setLineDash(isSelected ? [8, 4] : [5, 5])
    ctx.strokeRect(offsetX + region.x, offsetY + region.y, region.width, region.height)

    // 绘制序号标识
    ctx.fillStyle = isSelected ? '#00aa00' : isHovered ? '#66dd66' : '#44ff44'
    const centerX = offsetX + region.x + region.width / 2
    const centerY = offsetY + region.y + region.height / 2
    ctx.beginPath()
    ctx.arc(centerX, centerY, isSelected ? 20 : 18, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = 'white'
    ctx.font = `bold ${isSelected ? '18px' : '16px'} Arial`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(region.order.toString(), centerX, centerY)

    // 选中状态时绘制调整手柄
    if (isSelected) {
      drawRectangleHandles(ctx, offsetX + region.x, offsetY + region.y, region.width, region.height)
    }
  }

  ctx.restore()
}

// 绘制线条调整手柄
const drawLineHandles = (ctx, x1, y1, x2, y2, type) => {
  ctx.fillStyle = '#ffffff'
  ctx.strokeStyle = '#007bff'
  ctx.lineWidth = 2

  if (type === 'horizontal') {
    // 横线：在两端绘制圆形手柄
    const handles = [
      { x: x1 + 20, y: y1 },
      { x: x2 - 20, y: y2 }
    ]
    
    handles.forEach(handle => {
      ctx.beginPath()
      ctx.arc(handle.x, handle.y, 6, 0, 2 * Math.PI)
      ctx.fill()
      ctx.stroke()
    })
  } else if (type === 'vertical') {
    // 竖线：在两端绘制圆形手柄
    const handles = [
      { x: x1, y: y1 + 20 },
      { x: x2, y: y2 - 20 }
    ]
    
    handles.forEach(handle => {
      ctx.beginPath()
      ctx.arc(handle.x, handle.y, 6, 0, 2 * Math.PI)
      ctx.fill()
      ctx.stroke()
    })
  }
}

// 绘制矩形调整手柄
const drawRectangleHandles = (ctx, x, y, width, height) => {
  ctx.fillStyle = '#ffffff'
  ctx.strokeStyle = '#007bff'
  ctx.lineWidth = 2

  const handleSize = 8
  const handles = [
    { x: x, y: y }, // 左上
    { x: x + width / 2, y: y }, // 上中
    { x: x + width, y: y }, // 右上
    { x: x + width, y: y + height / 2 }, // 右中
    { x: x + width, y: y + height }, // 右下
    { x: x + width / 2, y: y + height }, // 下中
    { x: x, y: y + height }, // 左下
    { x: x, y: y + height / 2 } // 左中
  ]

  handles.forEach(handle => {
    ctx.fillRect(handle.x - handleSize / 2, handle.y - handleSize / 2, handleSize, handleSize)
    ctx.strokeRect(handle.x - handleSize / 2, handle.y - handleSize / 2, handleSize, handleSize)
  })
}

// 绘制临时矩形
const drawTemporaryRectangle = (x1, y1, x2, y2) => {
  if (!currentImage.value) return

  const canvas = imageCanvas.value
  const ctx = canvas.getContext('2d')
  const { offsetX, offsetY } = currentImage.value

  ctx.save()
  ctx.strokeStyle = '#888888'
  ctx.lineWidth = 1
  ctx.setLineDash([3, 3])

  const left = Math.min(x1, x2)
  const top = Math.min(y1, y2)
  const width = Math.abs(x2 - x1)
  const height = Math.abs(y2 - y1)

  // 添加偏移量
  ctx.strokeRect(offsetX + left, offsetY + top, width, height)
  ctx.restore()
}

// 清除所有分割
const clearSplits = () => {
  splitRegions.value = []
  selectedRegion.value = -1
  hoverRegion.value = -1
  hoverHandle.value = ''
  isDragging.value = false
  dragMode.value = ''
  // 清除后重置为选择工具
  currentTool.value = 'select'
  redrawCanvas()
}

// 撤销最后一个分割
const undoLastSplit = () => {
  if (splitRegions.value.length > 0) {
    splitRegions.value.pop()
    selectedRegion.value = -1
    hoverRegion.value = -1
    hoverHandle.value = ''
    redrawCanvas()
  }
}

// 缩放功能（通过滚轮和快捷键实现）

const resetZoom = () => {
  zoomLevel.value = 1.0
  transformOrigin.value = 'center center'
  // 不再清空分割状态，保持用户的分割线
}

const setZoomLevel = (newZoom, centerPoint = null) => {
  zoomLevel.value = newZoom
  if (currentImage.value) {
    updateImageDisplay(centerPoint)
  }
}

// 更新图片显示（重新计算尺寸并重绘）
const updateImageDisplay = (centerPoint = null) => {
  if (!currentImage.value) return

  // 重新计算显示尺寸
  const baseWidth = currentImage.value.baseDisplayWidth || currentImage.value.displayWidth
  const baseHeight = currentImage.value.baseDisplayHeight || currentImage.value.displayHeight

  // 保存基础尺寸（未缩放时的尺寸）
  if (!currentImage.value.baseDisplayWidth) {
    currentImage.value.baseDisplayWidth = currentImage.value.displayWidth
    currentImage.value.baseDisplayHeight = currentImage.value.displayHeight
  }

  // 记录缩放前的中心点（如果没有提供）
  if (!centerPoint && currentImage.value.displayWidth && currentImage.value.displayHeight) {
    centerPoint = {
      x: currentImage.value.offsetX + currentImage.value.displayWidth / 2,
      y: currentImage.value.offsetY + currentImage.value.displayHeight / 2
    }
  }

  // 应用缩放
  const scaledWidth = baseWidth * zoomLevel.value
  const scaledHeight = baseHeight * zoomLevel.value

  // 计算Canvas尺寸（确保能容纳完整的缩放后图片）
  // Canvas尺寸应该能完全容纳缩放后的图片，以便滚动条正常工作
  const CANVAS_WIDTH = scaledWidth + 40  // 给图片周围留一些边距
  const CANVAS_HEIGHT = scaledHeight + 40

  // 计算图片位置：如果有中心点，保持中心点位置；否则居中
  let offsetX, offsetY
  if (centerPoint && currentImage.value.displayWidth) {
    // 保持中心点位置不变（用于滚轮缩放）
    offsetX = centerPoint.x - scaledWidth / 2
    offsetY = centerPoint.y - scaledHeight / 2
  } else {
    // 居中显示（图片在Canvas中居中，Canvas周围有20px边距）
    offsetX = 20
    offsetY = 20
  }

  // 更新图片显示信息
  currentImage.value.displayWidth = scaledWidth
  currentImage.value.displayHeight = scaledHeight
  currentImage.value.canvasWidth = CANVAS_WIDTH
  currentImage.value.canvasHeight = CANVAS_HEIGHT
  currentImage.value.offsetX = offsetX
  currentImage.value.offsetY = offsetY

  // 更新分割线的显示坐标
  updateSplitRegionsDisplayCoords()

  // 重新绘制Canvas
  redrawCanvas()

  console.log('缩放更新:', {
    zoomLevel: zoomLevel.value,
    baseSize: `${baseWidth}x${baseHeight}`,
    scaledSize: `${scaledWidth}x${scaledHeight}`,
    canvasSize: `${CANVAS_WIDTH}x${CANVAS_HEIGHT}`,
    offset: `${offsetX},${offsetY}`,
    centerPoint: centerPoint
  })
}

// 确认分割
const confirmSplit = async () => {
  if (splitRegions.value.length === 0 || isProcessing.value) {
    return
  }

  try {
    isProcessing.value = true
    errorMessage.value = ''

    // 检查是否为演示图片
    if (currentImage.value && currentImage.value.isDemo) {
      throw new Error('请等待真实图片加载完成后再进行分割。当前显示的是演示图片，尺寸可能不准确。')
    }

    // 生成分割图片URL
    const splitImages = await generateSplitImagesWithOSS()

    if (splitImages.length > 0) {
      // 显示预览界面
      previewImages.value = splitImages
      showPreview.value = true
      
      console.log('分割URL生成成功，显示预览界面:', splitImages)
    }

  } catch (error) {
    console.error('分割预览失败:', error)
    errorMessage.value = '分割预览失败: ' + error.message
  } finally {
    isProcessing.value = false
  }
}

// 使用OSS图片处理参数生成分割后的图片URL
const generateSplitImagesWithOSS = async () => {
  if (!currentImage.value) {
    throw new Error('没有加载的图片')
  }

  // 检查是否有OSS URL
  if (!currentImage.value.ossUrl) {
    if (currentImage.value.isDemo) {
      throw new Error('演示图片无法进行真实分割，请等待真实图片加载完成后再试')
    } else {
      throw new Error('没有OSS图片URL，无法进行服务端分割')
    }
  }

  if (splitRegions.value.length === 0) {
    throw new Error('没有分割区域')
  }

  const splitImages = []

  console.log('开始生成OSS分割URL')
  console.log('原图尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
  console.log('分割区域使用相对坐标，直接转换为像素坐标')
  
  // 添加详细的坐标调试信息
  console.log('=== 坐标计算调试信息 ===')
  console.log('原图真实像素尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
  console.log('WPS显示尺寸:', currentImageInfo.value?.wpsDisplayWidth, 'x', currentImageInfo.value?.wpsDisplayHeight)
  console.log('使用相对坐标系统，转换为真实像素坐标进行分割')

  // 验证原图尺寸的合理性
  if (!currentImage.value.originalWidth || !currentImage.value.originalHeight) {
    console.error('原图尺寸无效:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
    throw new Error('原图尺寸信息缺失')
  }

  // 验证是否为合理的像素尺寸（应该远大于WPS显示尺寸）
  const isReasonablePixelSize = currentImage.value.originalWidth > 100 && currentImage.value.originalHeight > 100
  if (!isReasonablePixelSize) {
    console.warn('⚠️ 警告：原图尺寸看起来不像真实像素尺寸，可能仍在使用WPS显示尺寸')
    console.warn('原图尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
  } else {
    console.log('✅ 原图尺寸验证通过，使用真实像素尺寸')
  }

  console.log('分割区域数量:', splitRegions.value.length)
  splitRegions.value.forEach((region, index) => {
    if (region.type === 'horizontal') {
      const pixelY = region.relativeY * currentImage.value.originalHeight
      console.log(`横线${index + 1}: 相对坐标 y=${region.relativeY.toFixed(4)}, 真实像素坐标 y=${Math.round(pixelY)}`)
    } else if (region.type === 'vertical') {
      const pixelX = region.relativeX * currentImage.value.originalWidth
      console.log(`竖线${index + 1}: 相对坐标 x=${region.relativeX.toFixed(4)}, 真实像素坐标 x=${Math.round(pixelX)}`)
    } else if (region.type === 'rectangle') {
      const pixelX = region.relativeX * currentImage.value.originalWidth
      const pixelY = region.relativeY * currentImage.value.originalHeight
      const pixelW = region.relativeWidth * currentImage.value.originalWidth
      const pixelH = region.relativeHeight * currentImage.value.originalHeight
      console.log(`矩形${index + 1}: 相对坐标 (${region.relativeX.toFixed(4)}, ${region.relativeY.toFixed(4)}, ${region.relativeWidth.toFixed(4)}, ${region.relativeHeight.toFixed(4)})`)
      console.log(`矩形${index + 1}: 真实像素坐标 (${Math.round(pixelX)}, ${Math.round(pixelY)}, ${Math.round(pixelW)}, ${Math.round(pixelH)})`)
      
      // 验证矩形是否在合理范围内
      if (pixelX < 0 || pixelY < 0 || pixelX + pixelW > currentImage.value.originalWidth || pixelY + pixelH > currentImage.value.originalHeight) {
        console.warn(`⚠️ 矩形${index + 1}坐标超出图片范围！`)
      }
      if (pixelW < 10 || pixelH < 10) {
        console.warn(`⚠️ 矩形${index + 1}尺寸过小，可能不合理！`)
      }
    }
  })
  console.log('==========================')

  // 获取分割类型（每次只使用一种分割方式）
  const splitType = splitRegions.value[0].type

  // 辅助函数：构造OSS裁剪URL  
  const buildOSSCropUrl = (params) => {
    // 根据阿里云OSS文档，参数格式为 image/crop,param1,param2,...
    const cropParams = `image/crop,${params.join(',')}`
    const baseUrl = currentImage.value.ossUrl.split('?')[0] // 移除已有的查询参数
    return `${baseUrl}?x-oss-process=${cropParams}`
  }

  // 构造完整的OSS裁剪URL（矩形分割）
  const buildFullCropUrl = (x, y, width, height) => {
    const params = [
      `x_${Math.round(x)}`,
      `y_${Math.round(y)}`,
      `w_${Math.round(width)}`,
      `h_${Math.round(height)}`
    ]
    return buildOSSCropUrl(params)
  }

  // 构造横线分割URL（完整参数：x=0, y=起始y, w=图片宽度, h=条带高度）
  const buildHorizontalCropUrl = (y, height) => {
    const params = [
      `x_0`,
      `y_${Math.round(y)}`,
      `w_${Math.round(currentImage.value.originalWidth)}`,
      `h_${Math.round(height)}`
    ]
    return buildOSSCropUrl(params)
  }

  // 构造竖线分割URL（完整参数：x=起始x, y=0, w=条带宽度, h=图片高度）
  const buildVerticalCropUrl = (x, width) => {
    const params = [
      `x_${Math.round(x)}`,
      `y_0`,
      `w_${Math.round(width)}`,
      `h_${Math.round(currentImage.value.originalHeight)}`
    ]
    return buildOSSCropUrl(params)
  }

  let order = 1

  // 根据分割类型处理
  if (splitType === 'rectangle') {
    // 矩形分割：每个矩形都是独立的分割区域
    for (const region of splitRegions.value) {
      const sourceX = region.relativeX * currentImage.value.originalWidth
      const sourceY = region.relativeY * currentImage.value.originalHeight
      const sourceWidth = region.relativeWidth * currentImage.value.originalWidth
      const sourceHeight = region.relativeHeight * currentImage.value.originalHeight

      const ossUrl = buildFullCropUrl(sourceX, sourceY, sourceWidth, sourceHeight)

      console.log(`\n🔸 矩形分割区域${order}`)
      console.log(`  相对坐标: (${region.relativeX.toFixed(4)}, ${region.relativeY.toFixed(4)}, ${region.relativeWidth.toFixed(4)}, ${region.relativeHeight.toFixed(4)})`)
      console.log(`  真实像素坐标: (${Math.round(sourceX)}, ${Math.round(sourceY)}, ${Math.round(sourceWidth)}, ${Math.round(sourceHeight)})`)
      console.log(`  OSS裁剪参数: x_${Math.round(sourceX)},y_${Math.round(sourceY)},w_${Math.round(sourceWidth)},h_${Math.round(sourceHeight)}`)
      console.log(`  完整OSS URL: ${ossUrl}`)

      splitImages.push({
        ossUrl: ossUrl,
        order: order++,
        type: 'rectangle',
        region: region,
        cropParams: ossUrl.split('?x-oss-process=')[1]
      })
    }

  } else if (splitType === 'horizontal') {
    // 横线分割：按Y坐标分割成水平条带
    const yLines = splitRegions.value.map(r => r.relativeY * currentImage.value.originalHeight).sort((a, b) => a - b)

    console.log('横线分割相对Y坐标:', splitRegions.value.map(r => r.relativeY))
    console.log('横线分割原图Y坐标:', yLines)
    console.log('原图尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)

    // 生成分割区域：从图片顶部开始，到每条线，最后到图片底部
    const yPoints = [0, ...yLines, currentImage.value.originalHeight]

    console.log('横线分割Y坐标点:', yPoints)

    // 生成每个水平条带
    let totalHeight = 0
    for (let i = 0; i < yPoints.length - 1; i++) {
      const y = yPoints[i]
      const nextY = yPoints[i + 1]
      const height = nextY - y
      totalHeight += height

      if (height > 0) {
        // 横线分割：完整参数 (x=0, y=起始y, w=图片宽度, h=条带高度)
        const ossUrl = buildHorizontalCropUrl(y, height)
        console.log(`\n🔹 横线分割区域${order}`)
        console.log(`  条带范围: y=${Math.round(y)} 到 y=${Math.round(nextY)}, 高度=${Math.round(height)}`)
        console.log(`  OSS裁剪参数: x_0,y_${Math.round(y)},w_${Math.round(currentImage.value.originalWidth)},h_${Math.round(height)}`)
        console.log(`  完整OSS URL: ${ossUrl}`)

        splitImages.push({
          ossUrl: ossUrl,
          order: order++,
          type: 'horizontal_strip',
          cropParams: ossUrl.split('?x-oss-process=')[1]
        })
      }
    }
    console.log(`横线分割覆盖验证: 总高度=${Math.round(totalHeight)}, 原图高度=${Math.round(currentImage.value.originalHeight)}, 覆盖率=${Math.round(totalHeight/currentImage.value.originalHeight*100)}%`)

  } else if (splitType === 'vertical') {
    // 竖线分割：按X坐标分割成垂直条带
    const xLines = splitRegions.value.map(r => r.relativeX * currentImage.value.originalWidth).sort((a, b) => a - b)

    console.log('竖线分割相对X坐标:', splitRegions.value.map(r => r.relativeX))
    console.log('竖线分割原图X坐标:', xLines)
    console.log('原图尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)

    // 生成分割区域：从图片左边开始，到每条线，最后到图片右边
    const xPoints = [0, ...xLines, currentImage.value.originalWidth]

    console.log('竖线分割X坐标点:', xPoints)

    // 生成每个垂直条带
    let totalWidth = 0
    for (let i = 0; i < xPoints.length - 1; i++) {
      const x = xPoints[i]
      const nextX = xPoints[i + 1]
      const width = nextX - x
      totalWidth += width

      if (width > 0) {
        // 竖线分割：完整参数 (x=起始x, y=0, w=条带宽度, h=图片高度)
        const ossUrl = buildVerticalCropUrl(x, width)
        console.log(`\n🔸 竖线分割区域${order}`)
        console.log(`  条带范围: x=${Math.round(x)} 到 x=${Math.round(nextX)}, 宽度=${Math.round(width)}`)
        console.log(`  OSS裁剪参数: x_${Math.round(x)},y_0,w_${Math.round(width)},h_${Math.round(currentImage.value.originalHeight)}`)
        console.log(`  完整OSS URL: ${ossUrl}`)

        splitImages.push({
          ossUrl: ossUrl,
          order: order++,
          type: 'vertical_strip',
          cropParams: ossUrl.split('?x-oss-process=')[1]
        })
      }
    }
    console.log(`竖线分割覆盖验证: 总宽度=${Math.round(totalWidth)}, 原图宽度=${Math.round(currentImage.value.originalWidth)}, 覆盖率=${Math.round(totalWidth/currentImage.value.originalWidth*100)}%`)
  }

  // 按顺序排序
  splitImages.sort((a, b) => a.order - b.order)

  return splitImages
}

// 通过服务端下载OSS分割图片到缓存目录
const downloadSplitImageToCache = async (ossUrl, fileName) => {
  try {
    if (!wsClient) {
      throw new Error('WebSocket客户端不可用')
    }

    console.log('请求服务端下载分割图片:', ossUrl, fileName)

    const downloadResult = await wsClient.sendRequest('imageSplit', 'downloadSplitImage', {
      ossUrl: ossUrl,
      fileName: fileName
    })

    if (downloadResult && downloadResult.success) {
      console.log('分割图片下载到缓存成功:', downloadResult.localPath)
      return downloadResult.localPath
    } else {
      throw new Error(`下载失败: ${downloadResult.error || '未知错误'}`)
    }

  } catch (error) {
    console.error('下载分割图片失败:', error)
    throw error
  }
}

// 计算分割片段在WPS中应该显示的尺寸
const calculateSplitImageDisplaySize = (splitImage) => {
  if (!currentImageInfo.value || !currentImage.value) {
    console.warn('缺少图片信息，使用默认尺寸')
    return { width: 200, height: 200 }
  }

  const originalWpsWidth = currentImageInfo.value.wpsDisplayWidth
  const originalWpsHeight = currentImageInfo.value.wpsDisplayHeight
  const originalPixelWidth = currentImage.value.originalWidth
  const originalPixelHeight = currentImage.value.originalHeight

  console.log(`\n=== 计算片段${splitImage.order}的WPS显示尺寸 ===`)
  console.log('原图WPS显示尺寸:', originalWpsWidth, 'x', originalWpsHeight)
  console.log('原图真实像素尺寸:', originalPixelWidth, 'x', originalPixelHeight)

  // 解析OSS裁剪参数
  const cropParams = splitImage.cropParams // 格式: image/crop,x_0,y_100,w_1920,h_540
  console.log('OSS裁剪参数:', cropParams)
  
  const paramMatch = cropParams.match(/image\/crop,(.+)/)
  if (!paramMatch) {
    console.warn('无法解析OSS参数:', cropParams)
    return { width: 200, height: 200 }
  }

  const params = paramMatch[1].split(',')
  const cropInfo = {}
  params.forEach(param => {
    const [key, value] = param.split('_')
    if (!isNaN(value)) {
      cropInfo[key] = parseInt(value)
    }
  })

  console.log('解析的裁剪信息:', cropInfo)

  // 验证必要的参数
  if (!cropInfo.w || !cropInfo.h) {
    console.warn('OSS参数缺少宽度或高度信息:', cropInfo)
    return { width: 200, height: 200 }
  }

  // 计算片段在原图中的比例
  const widthRatio = cropInfo.w / originalPixelWidth
  const heightRatio = cropInfo.h / originalPixelHeight

  // 计算在WPS中应该显示的尺寸
  let displayWidth = originalWpsWidth * widthRatio
  let displayHeight = originalWpsHeight * heightRatio

  // 添加最小和最大尺寸限制
  const MIN_SIZE = 20 // 最小20像素
  const MAX_SIZE = 800 // 最大800像素
  
  displayWidth = Math.max(MIN_SIZE, Math.min(MAX_SIZE, displayWidth))
  displayHeight = Math.max(MIN_SIZE, Math.min(MAX_SIZE, displayHeight))

  console.log('尺寸比例:', { widthRatio: widthRatio.toFixed(3), heightRatio: heightRatio.toFixed(3) })
  console.log('计算的WPS显示尺寸:', Math.round(displayWidth), 'x', Math.round(displayHeight))
  console.log('==========================================\n')

  return {
    width: Math.round(displayWidth),
    height: Math.round(displayHeight)
  }
}

// 将分割后的图片按顺序插入到文档
const insertSplitImagesToDocument = async (splitImages) => {
  try {
    const selection = window.Application.Selection
    const document = window.Application.ActiveDocument

    if (!selection || !document) {
      throw new Error('无法访问文档或选择区域')
    }

    console.log('\n🖼️ === 开始插入分割图片到WPS文档 ===')
    console.log('总共需要插入的片段数量:', splitImages.length)
    console.log('原图WPS显示尺寸:', currentImageInfo.value?.wpsDisplayWidth, 'x', currentImageInfo.value?.wpsDisplayHeight)
    console.log('原图真实像素尺寸:', currentImage.value?.originalWidth, 'x', currentImage.value?.originalHeight)

    // 创建撤销记录，让所有操作成为一个整体
    console.log('📝 创建撤销记录，确保一次Ctrl+Z可以撤销所有操作...')
    let undoRecord = null
    let useUndoRecord = false
    
    try {
      // 尝试使用撤销记录功能
      undoRecord = window.Application.UndoRecord
      if (undoRecord && typeof undoRecord.StartCustomRecord === 'function') {
        undoRecord.StartCustomRecord('图片分割插入')
        useUndoRecord = true
        console.log('✅ 撤销记录已启动')
      } else {
        console.log('⚠️ 撤销记录功能不可用，将尝试其他方式')
      }
    } catch (undoError) {
      console.warn('启动撤销记录失败:', undoError)
      useUndoRecord = false
    }

    try {
      // 获取当前选中的图片范围
      const originalRange = selection.Range

      // 找到选中图片的位置，在其下方插入分割结果
      let insertPosition = originalRange.End

      // 如果不支持撤销记录，尝试使用批量操作方式
      if (!useUndoRecord) {
        console.log('🔄 使用批量操作方式减少撤销次数...')
        await insertSplitImagesInBatches(splitImages, document, insertPosition)
      } else {
        console.log('📝 使用标准方式插入图片...')
        await insertSplitImagesSequentially(splitImages, document, insertPosition)
      }

    console.log('\n✅ === 所有分割图片插入完成 ===')
    console.log('成功插入的片段数量:', splitImages.length)
    console.log('开始删除原始图片...')

    // 所有分割图片插入完成后，删除原始选中的图片
    try {
      // 重新获取选择，因为插入操作可能改变了选择状态
      const currentSelection = window.Application.Selection
      if (currentSelection && currentSelection.Range && currentSelection.Range.InlineShapes && currentSelection.Range.InlineShapes.Count > 0) {
        // 删除选中范围内的所有图片
        for (let i = currentSelection.Range.InlineShapes.Count; i >= 1; i--) {
          const shape = currentSelection.Range.InlineShapes.Item(i)
          if (shape && shape.Type === 3) { // 图片类型
            shape.Delete()
            console.log(`已删除原图片 ${i}`)
          }
        }
        console.log('✅ 原始图片删除完成')
      }
    } catch (deleteError) {
      console.warn('删除原图片失败:', deleteError)
      // 删除失败不影响整体流程，只记录警告
    }

      console.log('🎉 === 图片分割插入流程全部完成 ===\n')

      // 结束撤销记录
      if (useUndoRecord && undoRecord) {
        try {
          undoRecord.EndCustomRecord()
          console.log('✅ 撤销记录已完成，现在可以通过一次Ctrl+Z撤销所有操作')
        } catch (endError) {
          console.warn('结束撤销记录时出错:', endError)
        }
      } else {
        console.log('ℹ️ 未使用撤销记录功能')
      }

    } catch (innerError) {
      // 如果操作过程中出错，尝试取消撤销记录
      if (useUndoRecord && undoRecord) {
        try {
          undoRecord.EndCustomRecord()
          console.log('❌ 操作出错，撤销记录已取消')
        } catch (undoError) {
          console.warn('取消撤销记录时出错:', undoError)
        }
      }
      throw innerError
    }

  } catch (error) {
    console.error('插入分割图片到文档失败:', error)
    throw error
  }
}

// 检查当前选择是否包含图片
const checkImageSelection = () => {
  try {
    if (!window.Application || !window.Application.Selection) {
      hasSelectedImage.value = false
      selectionType.value = '无选择'
      imageCount.value = 0
      selectionText.value = ''
      return
    }

    const selection = window.Application.Selection
    const range = selection.Range

    if (!range) {
      hasSelectedImage.value = false
      selectionType.value = '无选择'
      imageCount.value = 0
      selectionText.value = ''
      return
    }

    // 获取选择的文本
    selectionText.value = selection.Text || ''

    // 检查是否有内联形状（图片）
    let inlineShapeCount = 0
    try {
      if (range.InlineShapes && range.InlineShapes.Count) {
        inlineShapeCount = range.InlineShapes.Count

        // 检查是否有图片类型的形状
        let imageShapeCount = 0
        for (let i = 1; i <= inlineShapeCount; i++) {
          const shape = range.InlineShapes.Item(i)
          // 检查形状类型是否为图片 (wdInlineShapePicture = 3)
          if (shape && shape.Type === 3) {
            imageShapeCount++
          }
        }
        imageCount.value = imageShapeCount
        hasSelectedImage.value = imageShapeCount > 0
      } else {
        imageCount.value = 0
        hasSelectedImage.value = false
      }
    } catch (e) {
      console.warn('检查内联形状时出错:', e)
      imageCount.value = 0
      hasSelectedImage.value = false
    }

    // 更新选择类型
    if (hasSelectedImage.value) {
      selectionType.value = `图片选择 (${imageCount.value}张)`
    } else if (selectionText.value.trim()) {
      selectionType.value = '文本选择'
    } else {
      selectionType.value = '空选择'
    }

  } catch (error) {
    console.error('检查图片选择时出错:', error)
    hasSelectedImage.value = false
    selectionType.value = '检查出错'
    imageCount.value = 0
    selectionText.value = ''
  }
}

// 旧的图片提取代码已删除，现在直接在saveSelectedImageToWatchDir中处理

// 旧的Base64读取代码已删除

// 旧的插入方法（已被新的insertSplitImagesToDocument替代）
// const insertSplitImages = async (splitResults) => {
//   // 这个方法已经被新的手动分割功能替代
// }

// 旧的插入方法已删除，使用新的insertSplitImagesToDocument方法

// 旧的单张图片插入方法已删除

// 旧的Base64文件保存方法已删除

// 旧的自动分割算法已删除，现在使用手动分割工具

// 旧的自动分割和预览代码已删除

// 设置WebSocket事件监听器
const setupWebSocketListeners = () => {
  if (!wsClient) {
    console.warn('WebSocket客户端不可用')
    return
  }

  // 监听imageSplit专用事件类型
  console.log('设置imageSplit事件监听器, wsClient:', wsClient)
  wsClient.addEventListener('imageSplit', (message) => {
    const { eventType, data } = message

    // 处理图片分割上传开始事件
    if (eventType === 'imageSplitUploadStart') {
      console.log('图片开始上传:', data.file)
      // 可以在这里显示上传进度
    }

    // 处理图片分割上传成功事件
    else if (eventType === 'imageSplitUploadSuccess') {
      handleImageUploadSuccess(data)
    }

    // 处理图片分割上传失败事件
    else if (eventType === 'imageSplitUploadError') {
      handleImageUploadError(data)
    }

    // 处理图片分割服务错误事件
    else if (eventType === 'imageSplitError') {
      handleImageSplitError(data)
    }
  })
}

// 处理图片上传成功事件
const handleImageUploadSuccess = async (data) => {
  console.log('图片上传成功:', data.file, data.ossUrl)

  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && data.file === currentImageInfo.value.fileName) {
    try {
      console.log('设置图片URL:', data.ossUrl)
      currentImageUrl.value = data.ossUrl

      // 设置图片信息（用于分割计算）
      currentImage.value = {
        originalPath: data.ossUrl,
        ossUrl: data.ossUrl, // 添加ossUrl属性，用于分割URL生成
        originalWidth: currentImageInfo.value.width,
        originalHeight: currentImageInfo.value.height,
        isDemo: false
      }

      console.log('图片URL设置成功，图片信息:', currentImage.value)
      successMessage.value = '图片加载成功！'
      setTimeout(() => {
        successMessage.value = ''
      }, 2000)
    } catch (loadError) {
      console.error('加载OSS图片到Canvas失败:', loadError)
      // 保持演示图片，但停止loading状态
      isLoadingImage.value = false
      errorMessage.value = '加载真实图片失败，使用演示图片'
      setTimeout(() => {
        errorMessage.value = ''
      }, 3000)
    }
  } else {
    // 其他分割图片上传成功的通知
    successMessage.value = `图片 ${data.file} 上传到OSS成功！`
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
  }
}

// 处理图片上传失败事件
const handleImageUploadError = (data) => {
  console.error('图片上传失败:', data.file, data.error)

  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && data.file === currentImageInfo.value.fileName) {
    console.log('原图上传失败，保持使用演示图片')
    isLoadingImage.value = false
    errorMessage.value = '图片上传失败，使用演示图片进行分割'
    setTimeout(() => {
      errorMessage.value = ''
    }, 3000)
  } else {
    // 其他分割图片上传失败的通知
    errorMessage.value = `图片 ${data.file} 上传失败: ${data.error}`
    setTimeout(() => {
      errorMessage.value = ''
    }, 5000)
  }
}

// 处理图片分割服务错误事件
const handleImageSplitError = (data) => {
  console.error('图片分割服务错误:', data.error, data.message)
  errorMessage.value = `图片分割服务错误: ${data.message}`
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

// 组件挂载时开始检查选择状态
onMounted(() => {
  checkImageSelection()
  // 每500ms检查一次选择状态
  selectionCheckTimer = setInterval(checkImageSelection, 500)

  // 设置WebSocket事件监听器
  setupWebSocketListeners()
  
  // 添加键盘事件监听器
  document.addEventListener('keydown', onKeyDown)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (selectionCheckTimer) {
    clearInterval(selectionCheckTimer)
  }
  
  // 清理键盘事件监听器
  document.removeEventListener('keydown', onKeyDown)
})

// 关闭预览界面
const closePreview = () => {
  showPreview.value = false
  previewImages.value = []
}

// 确认插入分割图片
const confirmInsertSplitImages = async () => {
  if (previewImages.value.length === 0 || isProcessing.value) {
    return
  }

  try {
    isProcessing.value = true
    errorMessage.value = ''

    // 插入图片到文档
    await insertSplitImagesToDocument(previewImages.value)

    // 关闭所有弹窗
    closePreview()
    closeSplitModal()

    // 显示成功消息
    successMessage.value = `分割完成！已插入 ${previewImages.value.length} 个图片片段到文档中。`
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)

  } catch (error) {
    console.error('插入分割图片失败:', error)
    errorMessage.value = '插入分割图片失败: ' + error.message
  } finally {
    isProcessing.value = false
  }
}

// 预览图片加载成功处理
const onPreviewImageLoad = (event) => {
  console.log('预览图片加载成功:', event.target.src)
}

// 预览图片加载错误处理
const onPreviewImageError = (event) => {
  console.error('预览图片加载失败:', event.target.src)
  // 可以在这里添加错误占位符
  event.target.style.display = 'none'
  const errorDiv = document.createElement('div')
  errorDiv.className = 'preview-image-error'
  errorDiv.textContent = '图片加载失败'
  event.target.parentNode.appendChild(errorDiv)
}

// 标准方式插入图片（逐个插入）
const insertSplitImagesSequentially = async (splitImages, document, startPosition) => {
  let insertPosition = startPosition

  console.log('📝 开始逐个插入图片...')
  
  // 按顺序插入每个分割后的图片
  for (let i = 0; i < splitImages.length; i++) {
    const splitImage = splitImages[i]

    try {
      console.log(`\n📥 正在处理第${i + 1}/${splitImages.length}个片段...`)
      console.log('片段类型:', splitImage.type, '| 片段编号:', splitImage.order)
      
      // 生成唯一的文件名
      const fileName = `split_${splitImage.type}_${splitImage.order}_${Date.now()}.jpg`

      // 先下载OSS图片到缓存目录
      console.log('⬇️ 下载OSS图片到本地缓存:', splitImage.ossUrl)
      const localPath = await downloadSplitImageToCache(splitImage.ossUrl, fileName)
      console.log('✅ 下载完成，本地路径:', localPath)

      // 使用本地路径插入图片
      console.log('📄 插入图片到WPS文档...')
      const imageRange = document.Range(insertPosition, insertPosition)
      const inlineShape = imageRange.InlineShapes.AddPicture(localPath)
      console.log('✅ 图片插入成功')

      // 计算并设置图片在WPS中的显示尺寸
      if (inlineShape) {
        const targetSize = calculateSplitImageDisplaySize(splitImage)
        
        console.log(`📏 设置片段${splitImage.order}的WPS显示尺寸: ${targetSize.width} x ${targetSize.height}`)
        
        // 设置计算出的尺寸
        inlineShape.Width = targetSize.width
        inlineShape.Height = targetSize.height
        
        console.log(`✅ 片段${splitImage.order}处理完成\n`)
      }

      insertPosition += 1 // 为插入的图片预留位置

      // 换行
      const lineBreakRange = document.Range(insertPosition, insertPosition)
      lineBreakRange.Text = '\n'
      insertPosition = lineBreakRange.End

    } catch (error) {
      console.error(`插入第${i + 1}个分割图片失败:`, error)

      // 如果插入图片失败，插入文本占位符
      const errorRange = document.Range(insertPosition, insertPosition)
      errorRange.Text = `[分割图片${i + 1}插入失败: ${splitImage.ossUrl}]\n`
      insertPosition = errorRange.End
    }
  }
}

// 批量方式插入图片（减少撤销次数）
const insertSplitImagesInBatches = async (splitImages, document, startPosition) => {
  console.log('🔄 开始批量插入图片...')
  
  try {
    // 第一步：预先下载所有图片
    console.log('⬇️ 第一步：预先下载所有图片到本地缓存...')
    const downloadPromises = splitImages.map(async (splitImage, index) => {
      const fileName = `split_${splitImage.type}_${splitImage.order}_${Date.now()}_${index}.jpg`
      try {
        const localPath = await downloadSplitImageToCache(splitImage.ossUrl, fileName)
        return { ...splitImage, localPath, fileName }
      } catch (error) {
        console.error(`下载图片 ${index + 1} 失败:`, error)
        return { ...splitImage, localPath: null, fileName, error }
      }
    })

    const downloadedImages = await Promise.all(downloadPromises)
    console.log('✅ 所有图片下载完成')

    // 第二步：分批插入图片（每批3-5张）
    console.log('📄 第二步：分批插入图片...')
    const batchSize = 3
    let insertPosition = startPosition
    
    for (let i = 0; i < downloadedImages.length; i += batchSize) {
      const batch = downloadedImages.slice(i, i + batchSize)
      console.log(`📦 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(downloadedImages.length / batchSize)}`)
      
      // 为当前批次构建内容
      let batchContent = ''
      const batchImages = []
      
      for (const imageData of batch) {
        if (imageData.localPath) {
          batchContent += `\n` // 为每张图片添加换行
          batchImages.push(imageData)
        } else {
          batchContent += `[分割图片${imageData.order}插入失败: ${imageData.error}]\n`
        }
      }
      
      // 插入批次内容
      if (batchContent) {
        const batchRange = document.Range(insertPosition, insertPosition)
        batchRange.Text = batchContent
        insertPosition = batchRange.End
      }
      
      // 插入批次中的图片
      let tempPosition = insertPosition - batchContent.length
      for (const imageData of batchImages) {
        try {
          // 找到插入位置（跳过换行符）
          tempPosition += 1
          const imageRange = document.Range(tempPosition, tempPosition)
          const inlineShape = imageRange.InlineShapes.AddPicture(imageData.localPath)
          
          if (inlineShape) {
            const targetSize = calculateSplitImageDisplaySize(imageData)
            inlineShape.Width = targetSize.width
            inlineShape.Height = targetSize.height
            console.log(`✅ 批次图片 ${imageData.order} 插入成功`)
          }
          
          tempPosition += 1
        } catch (error) {
          console.error(`批次插入图片 ${imageData.order} 失败:`, error)
          tempPosition += 1
        }
      }
    }
    
    console.log('✅ 批量插入完成')
    
  } catch (error) {
    console.error('批量插入过程中出错:', error)
    // 如果批量插入失败，回退到标准方式
    console.log('🔄 回退到标准插入方式...')
    await insertSplitImagesSequentially(splitImages, document, startPosition)
  }
}

const onCanvasMouseLeave = () => {
  // 停止所有交互操作
  isDrawing.value = false
  isDragging.value = false
  dragMode.value = ''
  hoverRegion.value = -1
  hoverHandle.value = ''
  
  // 恢复默认光标
  if (imageCanvas.value) {
    imageCanvas.value.style.cursor = 'crosshair'
  }
  
  // 重绘Canvas以清除临时绘制
  redrawCanvas()
}

// 鼠标滚轮缩放
const onCanvasWheel = (event) => {
  event.preventDefault()

  if (!currentImage.value) return

  const delta = event.deltaY > 0 ? -zoomStep.value : zoomStep.value
  const newZoom = Math.max(minZoom.value, Math.min(maxZoom.value, zoomLevel.value + delta))

  if (newZoom !== zoomLevel.value) {
    // 获取鼠标在Canvas中的位置作为缩放中心点
    const canvas = imageCanvas.value
    const rect = canvas.getBoundingClientRect()
    const centerPoint = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }

    // 设置新的缩放级别，并以鼠标位置为中心
    zoomLevel.value = newZoom
    updateImageDisplay(centerPoint)
  }
}

// 双击重置缩放
const onCanvasDoubleClick = (event) => {
  event.preventDefault()
  if (zoomLevel.value !== 1.0) {
    resetZoom()
  }
}

// 键盘快捷键支持
const onKeyDown = (event) => {
  if (!showSplitModal.value) return

  // Ctrl + 数字键或 + - 键进行缩放
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case '=':
      case '+': {
        event.preventDefault()
        const newZoom = Math.min(maxZoom.value, zoomLevel.value + zoomStep.value)
        zoomLevel.value = newZoom
        break
      }
      case '-': {
        event.preventDefault()
        const newZoom = Math.max(minZoom.value, zoomLevel.value - zoomStep.value)
        zoomLevel.value = newZoom
        break
      }
      case '0':
        event.preventDefault()
        resetZoom()
        break
    }
  }
}
</script>

<style scoped>
.image-split-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  height: 100vh;
  overflow-y: auto;
}

.header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 15px;
}

.header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.status-indicator {
  padding: 8px 12px;
  border-radius: 4px;
  display: inline-block;
}

.status-indicator.has-image {
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
}

.status-indicator:not(.has-image) {
  background-color: #fff3cd;
  border: 1px solid #ffc107;
}

.status-text.success {
  color: #2e7d32;
  font-weight: 500;
}

.status-text.warning {
  color: #f57c00;
  font-weight: 500;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.instruction-section,
.selection-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.instruction-section h3,
.selection-info h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
}

.instruction-section ol {
  margin: 0;
  padding-left: 20px;
}

.instruction-section li {
  margin-bottom: 5px;
  color: #6c757d;
}

.selection-details p {
  margin: 5px 0;
  color: #495057;
}

.action-section {
  text-align: center;
  margin: 20px 0;
}

.split-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.split-btn:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.split-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 小型加载动画（用于按钮） */
.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-top: 10px;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
  margin-top: 10px;
}

/* 分割选项样式 */
.split-options {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.split-options h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
}

.option-group {
  margin-bottom: 15px;
}

.option-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.radio-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.radio-item input[type="radio"] {
  margin: 0;
}

.grid-params,
.smart-params,
.custom-params {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.param-item label {
  min-width: 60px;
  font-size: 14px;
  color: #495057;
}

.param-item input[type="number"] {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.param-item input[type="range"] {
  flex: 1;
  max-width: 150px;
}

.param-value {
  min-width: 40px;
  font-size: 14px;
  color: #6c757d;
}

.param-info {
  margin-top: 8px;
  padding: 8px;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  color: #6c757d;
  font-style: italic;
}

/* 预览区域样式 */
.preview-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-top: 20px;
}

.preview-section h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
}

.preview-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.preview-grid.grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  min-width: 120px;
}

.preview-image {
  max-width: 100px;
  max-height: 100px;
  object-fit: contain;
  border: 1px solid #e9ecef;
  border-radius: 2px;
}

.preview-info {
  margin-top: 5px;
  text-align: center;
  font-size: 12px;
  color: #6c757d;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #545b62;
}

/* 顶部操作区域样式 */
.top-action-section {
  text-align: center;
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.start-split-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.start-split-btn:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.start-split-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 分割弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.split-modal {
  background: white;
  border-radius: 8px;
  width: 90vw;
  max-width: 1200px;
  height: 90vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #495057;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.tool-group {
  display: flex;
  gap: 8px;
}

.tool-btn {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 60px;
  height: 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.tool-icon {
  font-size: 14px;
  line-height: 1;
  /* 确保emoji图标正确显示 */
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif;
}

.tool-text {
  font-size: 11px;
  line-height: 1;
  white-space: nowrap;
}

.tool-btn:hover {
  background: #e9ecef;
}

.tool-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.tool-btn.active .tool-icon {
  color: white;
}

.tool-btn:disabled,
.tool-btn.disabled {
  background: #f8f9fa;
  color: #6c757d;
  border-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.6;
}

.tool-btn:disabled:hover,
.tool-btn.disabled:hover {
  background: #f8f9fa;
  color: #6c757d;
  transform: none;
}

.tool-btn:disabled .tool-icon,
.tool-btn.disabled .tool-icon {
  color: #6c757d;
}

.tool-actions {
  display: flex;
  gap: 10px;
}

.clear-btn,
.undo-btn {
  padding: 6px 12px;
  border: 1px solid #dc3545;
  background: white;
  color: #dc3545;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
  height: 36px;
}

.clear-btn:hover,
.undo-btn:hover {
  background: #dc3545;
  color: white;
}

/* 缩放控制样式 */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  border-left: 1px solid #e9ecef;
  border-right: 1px solid #e9ecef;
}

.zoom-btn {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.zoom-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.zoom-btn:disabled {
  background: #f8f9fa;
  color: #6c757d;
  border-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.6;
}

.zoom-btn.reset-zoom {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.zoom-btn.reset-zoom:hover:not(:disabled) {
  background: #0056b3;
  border-color: #004085;
}

.zoom-icon {
  font-size: 12px;
  line-height: 1;
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif;
}

.zoom-text {
  font-size: 12px;
  font-weight: 500;
}



/* 图片编辑区域样式 */
.image-editor {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px; /* 减少内边距，给图片更多空间 */
  background: #f8f9fa;
  overflow: hidden;
  position: relative;
  /* 为loading覆盖层提供定位基准 */
  min-height: 0; /* 确保flex子元素可以缩小 */
}

/* 新的图片容器样式 */
.image-container {
  width: 100%;
  height: 100%;
  max-width: calc(100vw - 60px);
  max-height: calc(100vh - 180px);
  overflow: auto;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #007bff #f8f9fa;
}

.image-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.image-container::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.image-container::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.image-container::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}

.image-container::-webkit-scrollbar-corner {
  background: #f8f9fa;
}

.image-wrapper {
  position: relative;
  display: inline-block;
  transition: transform 0.2s ease;
}

.split-image {
  display: block;
  max-width: none;
  max-height: none;
  user-select: none;
  cursor: crosshair;
}

/* 选择工具时的鼠标样式 */
.image-wrapper.select-tool .split-image {
  cursor: grab;
}

.image-wrapper.select-tool.panning .split-image {
  cursor: grabbing;
}

.split-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.split-line {
  position: absolute;
  pointer-events: auto;
  cursor: move;
  z-index: 10;
}

.horizontal-line {
  width: 100%;
  height: 3px;
  background: rgba(255, 68, 68, 0.8);
  border: 1px dashed #ff4444;
  transform: translateY(-50%);
}

.horizontal-line.selected {
  background: rgba(255, 0, 0, 0.9);
  border: 2px dashed #ff0000;
  height: 4px;
}

.horizontal-line.hovered {
  background: rgba(255, 102, 102, 0.9);
  border: 2px dashed #ff6666;
}

.vertical-line {
  height: 100%;
  width: 3px;
  background: rgba(68, 68, 255, 0.8);
  border: 1px dashed #4444ff;
  transform: translateX(-50%);
}

.vertical-line.selected {
  background: rgba(0, 0, 255, 0.9);
  border: 2px dashed #0000ff;
  width: 4px;
}

.vertical-line.hovered {
  background: rgba(102, 102, 255, 0.9);
  border: 2px dashed #6666ff;
}

.split-rectangle {
  position: absolute;
  border: 2px dashed #44ff44;
  background: rgba(68, 255, 68, 0.1);
  pointer-events: auto;
  cursor: move;
  z-index: 10;
}

.split-rectangle.selected {
  border: 3px dashed #00aa00;
  background: rgba(0, 170, 0, 0.15);
}

.split-rectangle.hovered {
  border: 3px dashed #66dd66;
  background: rgba(102, 221, 102, 0.15);
}

.split-line-label,
.split-rect-label {
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
  pointer-events: none;
  z-index: 11;
}

.split-line-label {
  top: -20px;
  left: 20px;
}

.split-rect-label {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  background: #007bff;
  border: 2px solid white;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  pointer-events: auto;
  z-index: 12;
}

.resize-handle.nw {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

.resize-handle.n {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.resize-handle.s {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.resize-handle.w {
  top: 50%;
  left: -4px;
  transform: translateY(-50%);
  cursor: w-resize;
}

.resize-handle.e {
  top: 50%;
  right: -4px;
  transform: translateY(-50%);
  cursor: e-resize;
}

/* 保留原有的Canvas容器样式以防需要 */
.image-canvas-container {
  display: none; /* 隐藏Canvas容器 */
}

.image-canvas-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.image-canvas-container::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.image-canvas-container::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.image-canvas-container::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}

.image-canvas-container::-webkit-scrollbar-corner {
  background: #f8f9fa;
}

.image-canvas {
  cursor: crosshair;
  display: block;
  background: white;
  /* 确保canvas在容器中正确显示 */
  margin: 0;
  padding: 0;
}



/* 弹窗底部样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.cancel-btn {
  padding: 10px 20px;
  border: 1px solid #6c757d;
  background: white;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #6c757d;
  color: white;
}

.confirm-btn {
  padding: 10px 20px;
  border: none;
  background: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.confirm-btn:hover:not(:disabled) {
  background: #218838;
}

/* Loading 覆盖层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  min-height: 200px; /* 确保有足够的高度显示加载动画 */
}

/* 大型加载动画（用于覆盖层） */
.loading-spinner-large {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
  display: block;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #007bff;
  font-size: 14px;
  font-weight: 500;
}

.confirm-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}



.split-tool-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 预览弹窗样式 */
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.preview-modal {
  background: white;
  border-radius: 8px;
  width: 90vw;
  max-width: 1200px;
  height: 90vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.preview-header h3 {
  margin: 0;
  color: #495057;
  font-size: 20px;
  font-weight: 600;
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-info-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.preview-info-header h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.preview-info-header p {
  margin: 6px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.preview-info-header p strong {
  color: #495057;
  font-weight: 600;
}

.preview-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(calc(var(--preview-item-width, 180px) + 16px), 1fr));
  gap: 20px;
  padding: 24px;
  overflow-y: auto;
  background: #f8f9fa;
  justify-items: center;
  align-items: start;
  place-items: start center;
}

/* 响应式网格 */
@media (max-width: 768px) {
  .preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(calc(var(--preview-item-width, 180px) * 0.8 + 12px), 1fr));
    gap: 16px;
    padding: 16px;
  }
  
  .preview-item {
    min-width: 100px;
    min-height: 100px;
  }
}

@media (min-width: 1200px) {
  .preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(calc(var(--preview-item-width, 180px) + 20px), 1fr));
    gap: 24px;
  }
}

.preview-item {
  background: white;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  padding: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: auto;
  height: auto;
  max-width: var(--preview-item-width, 180px);
  max-height: var(--preview-item-height, 180px);
  min-width: 120px;
  min-height: 120px;
}

.preview-item:hover {
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.10),
    0 3px 8px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px) scale(1.01);
  border-color: #007bff;
}

.preview-image-wrapper {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  min-height: 100px;
}

.preview-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: var(--preview-item-height, 180px);
  object-fit: contain;
  transition: all 0.3s ease;
}

.preview-item:hover .preview-image {
  transform: scale(1.02);
}

.preview-image-overlay {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 
    0 3px 8px rgba(0, 123, 255, 0.4),
    0 1px 3px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  border: 2px solid rgba(255, 255, 255, 0.9);
  min-width: 28px;
  text-align: center;
  transition: all 0.3s ease;
}

.preview-item:hover .preview-image-overlay {
  transform: scale(1.1);
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.preview-image-number {
  font-size: 14px;
  line-height: 1;
}

/* preview-item-info 样式已移除，因为用户隐藏了该内容 */

/* 矩形分割预览样式 */
.preview-rectangle-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(calc(var(--preview-item-width, 180px) + 16px), 1fr));
  gap: 20px;
  padding: 24px;
  overflow-y: auto;
  background: #f8f9fa;
  justify-items: center;
  align-items: start;
  place-items: start center;
}

/* 响应式网格 */
@media (max-width: 768px) {
  .preview-rectangle-grid {
    grid-template-columns: repeat(auto-fill, minmax(calc(var(--preview-item-width, 180px) * 0.8 + 12px), 1fr));
    gap: 16px;
    padding: 16px;
  }
}

@media (min-width: 1200px) {
  .preview-rectangle-grid {
    grid-template-columns: repeat(auto-fill, minmax(calc(var(--preview-item-width, 180px) + 20px), 1fr));
    gap: 24px;
  }
}

.preview-rectangle-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 4px;
  transition: all 0.3s ease;
  background: transparent;
  padding: 4px;
  width: auto;
  height: auto;
  max-width: var(--preview-item-width, 180px);
  max-height: var(--preview-item-height, 180px);
  min-width: 120px;
  min-height: 120px;
}

.preview-rectangle-item:hover {
  transform: scale(1.01);
}

.preview-rectangle-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: var(--preview-item-height, 180px);
  object-fit: contain;
  transition: all 0.3s ease;
  border-radius: 2px;
  min-height: 60px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-rectangle-item:hover .preview-rectangle-image {
  transform: scale(1.01);
}

.preview-rectangle-number {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.preview-rectangle-item:hover .preview-rectangle-number {
  background: #0056b3;
  transform: scale(1.1);
}

/* 条带布局样式 */
.preview-strips {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-strips-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  padding: 4px;
}

/* 垂直条带布局（横线分割） */
.preview-strips-vertical .preview-strips-container {
  display: flex;
  flex-direction: column;
  width: var(--strip-container-width, 600px);
  max-width: 800px;
  min-width: 300px;
}

.preview-strips-vertical .preview-strip-item {
  position: relative;
  border-bottom: 2px solid #e9ecef;
  overflow: hidden;
  padding: 4px;
}

.preview-strips-vertical .preview-strip-item:last-child {
  border-bottom: none;
}

.preview-strips-vertical .preview-strip-image {
  width: 100%;
  height: auto;
  display: block;
  transition: all 0.3s ease;
  object-fit: contain;
  min-height: 60px;
}

.preview-strips-vertical .preview-strip-item:hover .preview-strip-image {
  transform: scale(1.01);
}

/* 水平条带布局（竖线分割） */
.preview-strips-horizontal .preview-strips-container {
  display: flex;
  flex-direction: row;
  height: var(--strip-container-height, 400px);
  max-height: 500px;
  min-height: 200px;
}

.preview-strips-horizontal .preview-strip-item {
  position: relative;
  border-right: 2px solid #e9ecef;
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.preview-strips-horizontal .preview-strip-item:last-child {
  border-right: none;
}

.preview-strips-horizontal .preview-strip-image {
  height: 100%;
  width: auto;
  max-width: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
  min-width: 60px;
}

.preview-strips-horizontal .preview-strip-item:hover .preview-strip-image {
  transform: scale(1.01);
}

/* 条带序号标签 */
.preview-strip-number {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.preview-strip-item:hover .preview-strip-number {
  background: #0056b3;
  transform: scale(1.1);
}

.preview-image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  padding: 20px;
  line-height: 1.3;
}

.preview-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.preview-footer .confirm-btn {
  padding: 12px 24px;
  border: none;
  background: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.preview-footer .confirm-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.preview-footer .cancel-btn {
  padding: 12px 24px;
  border: 1px solid #6c757d;
  background: white;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

  .preview-footer .cancel-btn:hover {
    background: #6c757d;
    color: white;
  }

/* 响应式样式 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }
  
  .tool-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .zoom-controls {
    border: none;
    padding: 8px;
    justify-content: center;
  }
  
  .image-canvas-container {
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 400px);
  }
  
  .tool-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .image-split-container {
    padding: 10px;
  }
  
  .toolbar {
    padding: 8px;
  }
  
  .tool-btn {
    min-width: 50px;
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .zoom-btn {
    min-width: 28px;
    height: 28px;
    font-size: 10px;
  }
  
  .image-canvas-container {
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 450px);
  }
}

/* 缩放提示样式 */
.zoom-tip {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 5;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.image-editor:hover .zoom-tip {
  opacity: 1;
}
</style>
